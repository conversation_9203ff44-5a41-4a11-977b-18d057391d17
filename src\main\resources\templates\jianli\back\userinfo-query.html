<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  
  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/lib/layui/layui.js" charset="utf-8"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  
  <body>
    <div class="x-body">
        <form class="layui-form">

            <input type="hidden"  th:value="${ result.id}" id="id"  name="id"  autocomplete="off" class="layui-input">
            <div class="layui-form-item">
                <label for="name" class="layui-form-label">
                    <span class="x-red">*</span>姓名
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.name}" type="text" id="name" name="name" required="" lay-verify="name"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="sex" class="layui-form-label">
                    <span class="x-red">*</span>性别
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.sex}" type="text" id="sex" name="sex" required="" lay-verify="sex"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="age" class="layui-form-label">
                    <span class="x-red">*</span>年龄
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.age}" type="text" id="age" name="age" required="" lay-verify="age"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="job" class="layui-form-label">
                    <span class="x-red">*</span>应聘岗位
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.job}" type="text" id="job" name="job" required="" lay-verify="job"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="address" class="layui-form-label">
                    <span class="x-red">*</span>出生地
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.address}" type="text" id="address" name="address" required="" lay-verify="address"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="linkAddress" class="layui-form-label">
                    <span class="x-red">*</span>现住址
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.linkAddress}" type="text" id="linkAddress" name="linkAddress" required="" lay-verify="linkAddress"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="education" class="layui-form-label">
                    <span class="x-red">*</span>学历
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.education}" type="text" id="education" name="education" required="" lay-verify="education"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="school" class="layui-form-label">
                    <span class="x-red">*</span>学校
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.school}" type="text" id="school" name="school" required="" lay-verify="school"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="specialty" class="layui-form-label">
                    <span class="x-red">*</span>专业
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.specialty}" type="text" id="specialty" name="specialty" required="" lay-verify="specialty"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="linkQq" class="layui-form-label">
                    <span class="x-red">*</span>QQ
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.linkQq}" type="text" id="linkQq" name="linkQq" required="" lay-verify="linkQq"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="linkWechat" class="layui-form-label">
                    <span class="x-red">*</span>微信
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.linkWechat}" type="text" id="linkWechat" name="linkWechat" required="" lay-verify="linkWechat"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="linkPhone" class="layui-form-label">
                    <span class="x-red">*</span>联系电话
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.linkPhone}" type="text" id="linkPhone" name="linkPhone" required="" lay-verify="linkPhone"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="linkEmail" class="layui-form-label">
                    <span class="x-red">*</span>联系邮箱
                </label>
                <div class="layui-input-inline">
                    <input th:value="${result.linkEmail}" type="text" id="linkEmail" name="linkEmail" required="" lay-verify="linkEmail"
                           autocomplete="off" class="layui-input">
                </div>
            </div>

          <div class="layui-form-item">
              <label  class="layui-form-label">
              </label>
              <button  class="layui-btn" lay-filter="add" lay-submit="">
                  增加
              </button>
          </div>
      </form>
    </div>
    <script th:inline="javascript">
      layui.use(['form','layer'], function(){
          $ = layui.jquery;
        var form = layui.form
        ,layer = layui.layer;
      
        //自定义验证规则

          form.verify({
              name: function(value){
                  if(value.length <=0){
                      return '姓名不能为空';
                  }
              },
              sex: function(value){
                  if(value.length <=0){
                      return '性别不能为空';
                  }
              },
              age: function(value){
                  if(value.length <=0){
                      return '年龄不能为空';
                  }
              },
              address: function(value){
                  if(value.length <=0){
                      return '出生地不能为空';
                  }
              },
              linkAddress: function(value){
                  if(value.length <=0){
                      return '现住址不能为空';
                  }
              },
              specialty: function(value){
                  if(value.length <=0){
                      return '专业不能为空';
                  }
              },
              education: function(value){
                  if(value.length <=0){
                      return '学历不能为空';
                  }
              },
              school: function(value){
                  if(value.length <=0){
                      return '学校不能为空';
                  }
              },
              linkQq: function(value){
                  if(value.length <=0){
                      return 'QQ不能为空';
                  }
              },
              linkWechat: function(value){
                  if(value.length <=0){
                      return '微信不能为空';
                  }
              },
              linkPhone: function(value){
                  if(value.length <=0){
                      return '联系电话不能为空';
                  }
              },
              linkEmail: function(value){
                  if(value.length <=0){
                      return '联系邮箱不能为空';
                  }
              },
              job: function(value){
                  if(value.length <=0){
                      return '职位不能为空';
                  }
              }
          });

        //监听提交
        form.on('submit(add)', function(data){
            $.ajax({
                type: "POST",  //提交方式
                url: "/jianli/userinfo/edit.html",
                dataType: 'json',
                async: false,
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (result) {//返回数据根据结果进行相应的处理
                    if (result.code === 0) {
                        //发异步，把数据提交给php
                        layer.alert("操作成功", {icon: 6},function () {
                            // 获得frame索引
                            var index = parent.layer.getFrameIndex(window.name);
                            //关闭当前frame
                            parent.layer.close(index);
                            //刷新父页面
                            window.parent.location.reload();
                        });
                    } else {
                        layer.alert(result.msg);
                    }
                }
            });


          return false;
        });
        
        
      });
  </script>
    <script>var _hmt = _hmt || []; (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?b393d153aeb26b46e9431fabaf0f6190";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();</script>
  </body>

</html>