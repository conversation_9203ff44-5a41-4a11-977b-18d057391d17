<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.mapper.ArticleMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.object.ArticleDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="column_id" jdbcType="INTEGER" property="columnId" />
    <result column="tourist_id" jdbcType="INTEGER" property="touristId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.hongshen.boke.dao.object.ArticleDO">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, creat_time, user_id, description, column_id, tourist_id
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.hongshen.boke.dao.object.ArticleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.object.ArticleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from boke_article
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from boke_article
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.object.ArticleExample">
    delete from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.object.ArticleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_article (title, creat_time, user_id, 
      description, column_id, tourist_id, 
      content)
    values (#{title,jdbcType=VARCHAR}, #{creatTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=INTEGER}, 
      #{description,jdbcType=VARCHAR}, #{columnId,jdbcType=INTEGER}, #{touristId,jdbcType=INTEGER}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.object.ArticleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_article
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null">
        title,
      </if>
      <if test="creatTime != null">
        creat_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="columnId != null">
        column_id,
      </if>
      <if test="touristId != null">
        tourist_id,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        #{creatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="columnId != null">
        #{columnId,jdbcType=INTEGER},
      </if>
      <if test="touristId != null">
        #{touristId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.object.ArticleExample" resultType="java.lang.Long">
    select count(*) from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update boke_article
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.creatTime != null">
        creat_time = #{record.creatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.columnId != null">
        column_id = #{record.columnId,jdbcType=INTEGER},
      </if>
      <if test="record.touristId != null">
        tourist_id = #{record.touristId,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update boke_article
    set id = #{record.id,jdbcType=INTEGER},
      title = #{record.title,jdbcType=VARCHAR},
      creat_time = #{record.creatTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      column_id = #{record.columnId,jdbcType=INTEGER},
      tourist_id = #{record.touristId,jdbcType=INTEGER},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update boke_article
    set id = #{record.id,jdbcType=INTEGER},
      title = #{record.title,jdbcType=VARCHAR},
      creat_time = #{record.creatTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      column_id = #{record.columnId,jdbcType=INTEGER},
      tourist_id = #{record.touristId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.object.ArticleDO">
    update boke_article
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        creat_time = #{creatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="columnId != null">
        column_id = #{columnId,jdbcType=INTEGER},
      </if>
      <if test="touristId != null">
        tourist_id = #{touristId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.hongshen.boke.dao.object.ArticleDO">
    update boke_article
    set title = #{title,jdbcType=VARCHAR},
      creat_time = #{creatTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      column_id = #{columnId,jdbcType=INTEGER},
      tourist_id = #{touristId,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.object.ArticleDO">
    update boke_article
    set title = #{title,jdbcType=VARCHAR},
      creat_time = #{creatTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      column_id = #{columnId,jdbcType=INTEGER},
      tourist_id = #{touristId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.hongshen.boke.dao.object.ArticleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.object.ArticleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>