2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
javax.management.InstanceAlreadyExistsException: org.apache.activemq:type=Broker,brokerName=localhost
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:437)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1898)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:966)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:900)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:324)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:522)
	at org.apache.activemq.broker.jmx.ManagementContext.registerMBean(ManagementContext.java:409)
	at org.apache.activemq.broker.jmx.AnnotatedMBean.registerMBean(AnnotatedMBean.java:91)
	at org.apache.activemq.broker.BrokerService.startManagementContext(BrokerService.java:2627)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:621)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'email' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: javax.management.InstanceAlreadyExistsException: org.apache.activemq:type=Broker,brokerName=localhost 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:211)
	at org.apache.activemq.broker.BrokerService.addShutdownHook(BrokerService.java:2544)
	at org.apache.activemq.broker.BrokerService.doStartBroker(BrokerService.java:740)
	at org.apache.activemq.broker.BrokerService.startBroker(BrokerService.java:733)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:636)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'count' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: java.lang.IllegalStateException: Shutdown in progress 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:211)
	at org.apache.activemq.broker.BrokerService.addShutdownHook(BrokerService.java:2544)
	at org.apache.activemq.broker.BrokerService.doStartBroker(BrokerService.java:740)
	at org.apache.activemq.broker.BrokerService.startBroker(BrokerService.java:733)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:636)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'testQueue' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: java.lang.IllegalStateException: Shutdown in progress 
