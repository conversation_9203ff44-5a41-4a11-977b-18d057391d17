package com.hongshen.boke.dao.jianli.object;

import java.util.ArrayList;
import java.util.List;

public class ProjectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectSkillIsNull() {
            addCriterion("project_skill is null");
            return (Criteria) this;
        }

        public Criteria andProjectSkillIsNotNull() {
            addCriterion("project_skill is not null");
            return (Criteria) this;
        }

        public Criteria andProjectSkillEqualTo(String value) {
            addCriterion("project_skill =", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillNotEqualTo(String value) {
            addCriterion("project_skill <>", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillGreaterThan(String value) {
            addCriterion("project_skill >", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillGreaterThanOrEqualTo(String value) {
            addCriterion("project_skill >=", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillLessThan(String value) {
            addCriterion("project_skill <", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillLessThanOrEqualTo(String value) {
            addCriterion("project_skill <=", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillLike(String value) {
            addCriterion("project_skill like", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillNotLike(String value) {
            addCriterion("project_skill not like", value, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillIn(List<String> values) {
            addCriterion("project_skill in", values, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillNotIn(List<String> values) {
            addCriterion("project_skill not in", values, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillBetween(String value1, String value2) {
            addCriterion("project_skill between", value1, value2, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectSkillNotBetween(String value1, String value2) {
            addCriterion("project_skill not between", value1, value2, "projectSkill");
            return (Criteria) this;
        }

        public Criteria andProjectTimeIsNull() {
            addCriterion("project_time is null");
            return (Criteria) this;
        }

        public Criteria andProjectTimeIsNotNull() {
            addCriterion("project_time is not null");
            return (Criteria) this;
        }

        public Criteria andProjectTimeEqualTo(String value) {
            addCriterion("project_time =", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeNotEqualTo(String value) {
            addCriterion("project_time <>", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeGreaterThan(String value) {
            addCriterion("project_time >", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeGreaterThanOrEqualTo(String value) {
            addCriterion("project_time >=", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeLessThan(String value) {
            addCriterion("project_time <", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeLessThanOrEqualTo(String value) {
            addCriterion("project_time <=", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeLike(String value) {
            addCriterion("project_time like", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeNotLike(String value) {
            addCriterion("project_time not like", value, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeIn(List<String> values) {
            addCriterion("project_time in", values, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeNotIn(List<String> values) {
            addCriterion("project_time not in", values, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeBetween(String value1, String value2) {
            addCriterion("project_time between", value1, value2, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectTimeNotBetween(String value1, String value2) {
            addCriterion("project_time not between", value1, value2, "projectTime");
            return (Criteria) this;
        }

        public Criteria andProjectClassIsNull() {
            addCriterion("project_class is null");
            return (Criteria) this;
        }

        public Criteria andProjectClassIsNotNull() {
            addCriterion("project_class is not null");
            return (Criteria) this;
        }

        public Criteria andProjectClassEqualTo(String value) {
            addCriterion("project_class =", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassNotEqualTo(String value) {
            addCriterion("project_class <>", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassGreaterThan(String value) {
            addCriterion("project_class >", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassGreaterThanOrEqualTo(String value) {
            addCriterion("project_class >=", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassLessThan(String value) {
            addCriterion("project_class <", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassLessThanOrEqualTo(String value) {
            addCriterion("project_class <=", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassLike(String value) {
            addCriterion("project_class like", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassNotLike(String value) {
            addCriterion("project_class not like", value, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassIn(List<String> values) {
            addCriterion("project_class in", values, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassNotIn(List<String> values) {
            addCriterion("project_class not in", values, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassBetween(String value1, String value2) {
            addCriterion("project_class between", value1, value2, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectClassNotBetween(String value1, String value2) {
            addCriterion("project_class not between", value1, value2, "projectClass");
            return (Criteria) this;
        }

        public Criteria andProjectImgIsNull() {
            addCriterion("project_img is null");
            return (Criteria) this;
        }

        public Criteria andProjectImgIsNotNull() {
            addCriterion("project_img is not null");
            return (Criteria) this;
        }

        public Criteria andProjectImgEqualTo(String value) {
            addCriterion("project_img =", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgNotEqualTo(String value) {
            addCriterion("project_img <>", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgGreaterThan(String value) {
            addCriterion("project_img >", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgGreaterThanOrEqualTo(String value) {
            addCriterion("project_img >=", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgLessThan(String value) {
            addCriterion("project_img <", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgLessThanOrEqualTo(String value) {
            addCriterion("project_img <=", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgLike(String value) {
            addCriterion("project_img like", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgNotLike(String value) {
            addCriterion("project_img not like", value, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgIn(List<String> values) {
            addCriterion("project_img in", values, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgNotIn(List<String> values) {
            addCriterion("project_img not in", values, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgBetween(String value1, String value2) {
            addCriterion("project_img between", value1, value2, "projectImg");
            return (Criteria) this;
        }

        public Criteria andProjectImgNotBetween(String value1, String value2) {
            addCriterion("project_img not between", value1, value2, "projectImg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}