<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  
  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <!--<script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>-->
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  
  <body>
    <div class="x-nav">
      <span class="layui-breadcrumb">
        <a href="">首页</a>
        <a href="">简历管理</a>
        <a>
          <cite>基本信息</cite></a>
      </span>
      <a class="layui-btn layui-btn-small" style="line-height:1.6em;margin-top:3px;float:right" href="javascript:location.replace(location.href);" title="刷新">
        <i class="layui-icon" style="line-height:30px">ဂ</i></a>
    </div>
    <div id="addUserinfo"  class="x-body">
      <!--<xblock>-->
        <!--<button class="layui-btn" onclick="x_admin_show('添加基本信息','/jianli/userinfo/userinfo-edit.html')"><i class="layui-icon"></i>添加</button>-->
        <!--<button title="每次添加会直接覆盖前一次上传的内容！" class="layui-btn" onclick="x_admin_show('添加简历','/jianli/userinfo/jianli-upload.html',500,500)"><i class="layui-icon"></i>简历上传</button>-->
      <!--</xblock>-->
    </div>
    <table    class="layui-table list-table1" id="test-table-checkbox" ></table>

    <script type="text/html" id="inventory_option">
        <a title="编辑"  onclick="x_admin_show('编辑','/jianli/userinfo/query.html?id={{d.id}}')" href="javascript:;">
          <i class="layui-icon">&#xe642;</i>
        </a>
        <!--<a onclick="x_admin_show('修改密码','member-password.html',600,400)" title="修改密码" href="javascript:;">-->
          <!--<i class="layui-icon">&#xe631;</i>-->
        <!--</a>-->
        <a title="删除" onclick="member_del(this,'{{d.id}}')" href="javascript:;">
          <i class="layui-icon">&#xe640;</i>
        </a>
    </script>
    <script type="text/javascript" src="/layui/layui.js"></script>
    <script  th:inline="javascript">
      layui.use(['form', 'laydate', 'table','jquery'], function(){
        var laydate = layui.laydate;
        var table = layui.table,$ = layui.jquery,form = layui.form;

        //执行一个laydate实例
        laydate.render({
          elem: '#start' //指定元素
        });

        //执行一个laydate实例
        laydate.render({
          elem: '#end' //指定元素
        });

          table.render({
              elem: '#test-table-checkbox'
              ,url:'/jianli/userinfo/list.html'
              ,page: true
              ,id: 'testReload'
              ,cols: [
                  [{
                      field: 'name',
                      title: '姓名'
                  }, {
                      field: 'sex',
                      title: '性别'
                  }, {
                      field: 'age',
                      title: '年龄'

                  }, {
                    field: 'address',
                    title: '出生地址'

                  },{
                    field: 'linkAddress',
                    title: '现住址'

                  },{
                    field: 'specialty',
                    title: '专业'

                  },{
                    field: 'education',
                    title: '学历'

                  },{
                    field: 'school',
                    title: '学校'

                  },{
                    field: 'linkQq',
                    title: 'QQ'

                  },{
                    field: 'linkWechat',
                    title: '微信'

                  },{
                    field: 'linkPhone',
                    title: '联系电话'

                  },{
                    field: 'linkEmail',
                    title: '联系邮箱'

                  },{
                    field: 'job',
                    title: '应聘岗位'

                  }, {
                      field: 'id',
                      title: '操作',
                      templet: '#inventory_option'
                  }]
              ]
            ,done: function(res, curr, count){
              //如果是异步请求数据方式，res即为你接口返回的信息。
              //如果是直接赋值的方式，res即为：{data: [], count: 99} data为当前页数据、count为数据总长度
              console.log(res);

              //得到当前页码
              console.log(curr);

              //得到数据总量
              console.log(count);
              var str='';
              if (count == 0){
               str="<xblock>" +
               "<button class=\"layui-btn\" onclick=\"x_admin_show('添加基本信息','/jianli/userinfo/userinfo-edit.html')\"><i class=\"layui-icon\"></i>添加</button>" +
               "</xblock>";
              }else {
                str="<xblock>" +
                "<button title=\"每次添加会直接覆盖前一次上传的内容！\" class=\"layui-btn\" onclick=\"x_admin_show('添加简历','/jianli/userinfo/jianli-upload.html',500,500)\"><i class=\"layui-icon\"></i>简历上传</button>" +
                "</xblock>";
              }
              document.getElementById("addUserinfo").innerHTML = str;
            }
          });

      });



      /*用户-删除*/
      function member_del(obj,id){
          layer.confirm('确认要删除吗？',function(index){
              //发异步删除数据
              $.ajax({
                  type: "POST",  //提交方式
                  url: "/jianli/userinfo/del.html?id="+id,
                  dataType: 'json',
                  async: false,
                  contentType: 'application/json',
                  success: function (result) {//返回数据根据结果进行相应的处理
                      if (result.code === 0) {
                          //发异步，把数据提交给
                          $(obj).parents("tr").remove();
                          layer.msg('已删除!',{icon:1,time:1000});
                      } else {
                          layer.alert(result.msg);
                      }
                  }
              });

          });
      }

    </script>
    <!--<script>var _hmt = _hmt || []; (function() {-->
        <!--var hm = document.createElement("script");-->
        <!--hm.src = "https://hm.baidu.com/hm.js?b393d153aeb26b46e9431fabaf0f6190";-->
        <!--var s = document.getElementsByTagName("script")[0];-->
        <!--s.parentNode.insertBefore(hm, s);-->
      <!--})();</script>-->
  </body>

</html>