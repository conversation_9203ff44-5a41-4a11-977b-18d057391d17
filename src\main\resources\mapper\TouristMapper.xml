<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.mapper.TouristMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.object.TouristDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="mailbox" jdbcType="VARCHAR" property="mailbox" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="head_portrait" jdbcType="VARCHAR" property="headPortrait" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, nickname, mailbox, username, password, head_portrait, creat_time
  </sql>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.object.TouristExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_tourist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from boke_tourist
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from boke_tourist
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.object.TouristExample">
    delete from boke_tourist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.object.TouristDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_tourist (nickname, mailbox, username, 
      password, head_portrait, creat_time
      )
    values (#{nickname,jdbcType=VARCHAR}, #{mailbox,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{headPortrait,jdbcType=VARCHAR}, #{creatTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.object.TouristDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_tourist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="nickname != null">
        nickname,
      </if>
      <if test="mailbox != null">
        mailbox,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="headPortrait != null">
        head_portrait,
      </if>
      <if test="creatTime != null">
        creat_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="mailbox != null">
        #{mailbox,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="headPortrait != null">
        #{headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.object.TouristExample" resultType="java.lang.Long">
    select count(*) from boke_tourist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update boke_tourist
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.mailbox != null">
        mailbox = #{record.mailbox,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.headPortrait != null">
        head_portrait = #{record.headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="record.creatTime != null">
        creat_time = #{record.creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update boke_tourist
    set id = #{record.id,jdbcType=INTEGER},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      mailbox = #{record.mailbox,jdbcType=VARCHAR},
      username = #{record.username,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      head_portrait = #{record.headPortrait,jdbcType=VARCHAR},
      creat_time = #{record.creatTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.object.TouristDO">
    update boke_tourist
    <set>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="mailbox != null">
        mailbox = #{mailbox,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="headPortrait != null">
        head_portrait = #{headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="creatTime != null">
        creat_time = #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.object.TouristDO">
    update boke_tourist
    set nickname = #{nickname,jdbcType=VARCHAR},
      mailbox = #{mailbox,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      head_portrait = #{headPortrait,jdbcType=VARCHAR},
      creat_time = #{creatTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.object.TouristExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_tourist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>