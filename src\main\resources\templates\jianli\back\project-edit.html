<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  
  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/lib/layui/layui.js" charset="utf-8"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  
  <body>
    <div class="x-body">
        <form class="layui-form">


            <div class="layui-form-item">
                <label for="projectName" class="layui-form-label">
                    <span class="x-red">*</span>项目名称
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="projectName" name="projectName" required="" lay-verify="projectName"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="projectSkill" class="layui-form-label">
                    <span class="x-red">*</span>使用技术
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="projectSkill" name="projectSkill" required="" lay-verify="projectSkill"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="projectTime" class="layui-form-label">
                    <span class="x-red">*</span>项目周期
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="projectTime" name="projectTime" required="" lay-verify="projectTime"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <!--<div class="layui-form-item">-->
                <!--<label for="projectClass" class="layui-form-label">-->
                    <!--<span class="x-red">*</span>项目类别-->
                <!--</label>-->
                <!--<div class="layui-input-inline">-->
                    <!--<input type="text" id="projectClass" name="projectClass" required="" lay-verify="projectClass"-->
                           <!--autocomplete="off" class="layui-input">-->
                <!--</div>-->
            <!--</div>-->


            <div class="layui-form-item">
                <label for="projectImg" class="layui-form-label">
                    项目主图
                </label>
                <input type="hidden"  id="projectImg" name="projectImg"  />
                <input id="fileheadPortrait" type="file" name="fileheadPortrait"/>
                <input type="button"  value="上传" onclick="myFunction(0)" />
                <div id="headPortraitImg"></div>
            </div>


            <div class="layui-form-item">
                <label for="projectDetails" class="layui-form-label">
                    <span class="x-red">*</span>项目详情
                </label>
                <div class="layui-input-block">
                    <input  placeholder="请输入内容" id="projectDetails" name="projectDetails" class="layui-textarea">
                </div>
            </div>
          <div class="layui-form-item">
              <label  class="layui-form-label">
              </label>
              <button  class="layui-btn" lay-filter="add" lay-submit="">
                  增加
              </button>
          </div>
      </form>
    </div>
    <script th:inline="javascript">
      layui.use(['form','layer'], function(){
          $ = layui.jquery;
        var form = layui.form
        ,layer = layui.layer;
      
        //自定义验证规则

          form.verify({
              projectName: function(value){
                  if(value.length <=0){
                      return '项目名称不能为空';
                  }
              },
              projectSkill: function(value){
                  if(value.length <=0){
                      return '使用技术不能为空';
                  }
              },
              projectTime: function(value){
                  if(value.length <=0){
                      return '项目周期不能为空';
                  }
              },
              projectDetails: function(value){
                  if(value.length <=0){
                      return '项目详情不能为空';
                  }
              }
          });

        //监听提交
        form.on('submit(add)', function(data){
            $.ajax({
                type: "POST",  //提交方式
                url: "/jianli/project/edit.html",
                dataType: 'json',
                async: false,
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (result) {//返回数据根据结果进行相应的处理
                    if (result.code === 0) {
                        //发异步，把数据提交给php
                        layer.alert("增加成功", {icon: 6},function () {
                            // 获得frame索引
                            var index = parent.layer.getFrameIndex(window.name);
                            //关闭当前frame
                            parent.layer.close(index);
                            //刷新父页面
                            window.parent.location.reload();
                        });
                    } else {
                        layer.alert(result.msg);
                    }
                }
            });


          return false;
        });



        
      });

      function myFunction(data){
          var formData = new FormData();
          formData.append('file', $('#fileheadPortrait')[0].files[0]);
          $.ajax({
              type: "POST",  //提交方式
              url: "/headImgUpload.html",
              contentType: false,
              processData: false,
              data: formData,
              success: function (result) {//返回数据根据结果进行相应的处理
                  if (result.code === 0){
                      alert(result.msg);
                     document.getElementById("projectImg").value = result.data;
                     document.getElementById("headPortraitImg").innerHTML="<img src=\""+ result.data+"\" width=\"50\" height=\"50\">";
                  }else {
                      alert(result.msg);
                  }

              }
          });
      }
  </script>
    <script>var _hmt = _hmt || []; (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?b393d153aeb26b46e9431fabaf0f6190";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();</script>
  </body>

</html>