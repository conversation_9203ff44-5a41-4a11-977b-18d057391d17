<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<!--<head class="panelBar" th:replace="before/title::titls"></head>-->
<head th:fragment="titls">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>文章详情</title>
	<meta name="description" content="" />
	<meta name="keywords" content="" />
	<link rel="stylesheet" type="text/css" href="/statics/css/index.css" media="all" />
	<link rel="stylesheet" href="/layui/css/layui.css">
	<script type="text/javascript" src="/layui/layui.js"></script>
</head>

	<body class="home blog custom-background round-avatars" itemscope itemtype="http://schema.org/Organization">
		<div th:replace="before/title::pagination" ></div>

		<div id="main" class="content">
			<div class="container">
				<article id="post-1202" class="js-gallery post-1202 post type-post status-publish format-standard has-post-thumbnail hentry category-uncategorized tag-11 tag-22 tag-29">
					<style>
						.container {
							padding: 10px 0px;
						}

						.post {
							margin: 0 auto
						}
					</style>
					<section class="post_content">
						<header class="post_header">
							<h1 class="post_title" th:text="${ article.title}"></h1>
						</header>
						<div class="post-body js-gallery" id="cont">
							<!--<p th:text="${ article.content}"></p>-->
						</div>


						<div class="meta split split--responsive cf">
							<div class="split__title">
								<p  th:text="${#dates.format(article.creatTime, 'yyyy-MM-dd')}"></p>
								<!--<span class=""><a href="" rel="tag">主题</a><a href="" rel="tag">日常</a><a href="" rel="tag">更新</a> </span>-->
							</div>
							<!--<div id="social-share"><span class="entypo-share"><i class="iconfont">&#xe614;</i>分享</span></div>-->
							<div class="slide">
								<a class="btn-slide" title="switch down"><i class="iconfont">&#xe615;</i>折叠评论区</a>
							</div>
						</div>
					</section>
				</article>
			</div>
			<svg id="upTriangleColor" width="100%" height="40" viewBox="0 0 100 102" preserveAspectRatio="none">
				<path d="M0 0 L50 100 L100 0 Z"></path>
			</svg>
			<div id="social">
				<ul>
					<li>
						<a href="" title="qzone" target="_blank"><i class="iconfont">&#xe67f;</i></a>
					</li>
					<li>
						<a href="" title="weibo" target="_blank"><i class="iconfont">&#xe680;</i></a>
					</li>
					<li>
						<a href="" title="douban" target="_blank"><i class="iconfont">&#xe681;</i></a>
					</li>
					<li>
						<a href="" title="twitter" target="_blank"><i class="iconfont">&#xe683;</i></a>
					</li>
				</ul>
			</div>
			<div id="panel">
				<div class="comment-area">
					<section class="comments">
						<div class="comments-main">
							<div id="comments-list-title"><span th:text="${ CommentsList.size()}"></span> 条评论 </div>
							<div id="loading-comments">
								<div class="host">
									<div class="loading loading-0"></div>
									<div class="loading loading-1"></div>
									<div class="loading loading-2"></div>
								</div>
							</div>
							<div id="content"  th:each="comments,commentsStat:${ CommentsList}">
								<div id="comments"></div>
								<div>
									<ul class="commentwrap" >
										<li class="comment even thread-even depth-1" id="li-comment-">
											<div id="comment-969" class="comment_body contents">
												<div class="profile">
													<a href=""><img src="/statics/images/9cc50a9e422fb1c89aebafeb959cef7a.jpg" class="gravatar" alt="小布丁"></a>
												</div>
												<div class="main shadow">
													<div class="commentinfo">
														<section class="commeta">
															<div class="shang">
																<h4 class="author"><a href="" target="_blank"><img src="/statics/images/9cc50a9e422fb1c89aebafeb959cef7a.jpg" class="gravatarsmall" alt="小布丁" th:text="${comments.nickname}"></a></h4>
															</div>
														</section>
													</div>
													<div class="body">
														<p th:text="${comments.leaveComments}"></p>
													</div>
													<div class="xia info">
														<span><time th:text="${#dates.format(comments.creatTime, 'yyyy-MM-dd')}" ></time></span>
														<!--<span><a rel='nofollow' class='comment-reply-link' href="" onclick='return addComment.moveForm( "comment-969", "969", "respond", "1202" )' aria-label='回复给小布丁'>回复</a></span>-->
													</div>
												</div>
											</div>
										</li>
										<!-- #comment-## -->
									</ul>
								</div>

							</div>
							<div id="respond" class="comment-respond">
								<h6 id="replytitle" class="comment-reply-title"><a rel="nofollow" id="cancel-comment-reply-link" href="" style="display:none;">取消</a></h6>
								<!--<form action="#" method="post" id="commentform" class="clearfix">-->
									<!--<div class="clearfix"></div>-->
									<!--<div class="author-info">-->
										<!--<input type="text" name="nickname" id="nickname" placeholder="昵  称 : " value="" tabindex="1" title="nickname (required)" />-->
										<!--<input type="text" name="mailbox" id="mailbox" placeholder="邮  箱 : " value="" tabindex="2" title="mailbox(will not be published) required" />-->
									<!--</div>-->
									<!--<div class="author-info">-->
										<!--<input type="password" name="password" id="password" placeholder="密 码 : " value="" tabindex="3" title="Website" />-->
										<!--<input type="password" name="passw" id="passw" placeholder="确认密码 : " value="" tabindex="3" title="Website" />-->
									<!--</div>-->

									<!--<div class="clearfix"></div>-->
									<!--<input type='hidden' name='comment_post_ID' value='1202' id='comment_post_ID' />-->
									<!--<input type='hidden' name='comment_parent' id='comment_parent' value='0' />-->
									<!--<p style="display: none;"><input type="hidden" id="akismet_comment_nonce" name="akismet_comment_nonce" value="632104fca1" /></p>-->
									<!--<p style="display: none;"><input type="hidden" id="ak_js" name="ak_js" value="87" /></p>-->
									<!--<div class="comment-form-info">-->
										<!--<div class="real-time-gravatar"> <img id="real-time-gravatar" src="/statics/images/d41d8cd98f00b204e9800998ecf8427e.png" alt="gravatar头像" />-->
										<!--</div>-->
										<!--<p class="input-row">-->
											<!--<i class="row-icon"></i>-->
											<!--<textarea class="text_area" rows="3" cols="80" name="leaveComments" id="leaveComments" tabindex="4" placeholder="你不说两句吗？(°∀°)ﾉ……"></textarea>-->
											<!--<input type="submit" name="submit" class="button" id="submit" tabindex="5" value="发送" />-->
										<!--</p>-->
									<!--</div>-->
								<!--</form>-->
								<form action="/tourist/add.html" method="post" id="commentform" class="clearfix">
									<input type="hidden"  th:value="${ article.id}" id="articleId"  name="articleId"  autocomplete="off" class="layui-input">
									<div class="clearfix"></div>
									<div class="clearfix"></div>
									<input type='hidden' name='comment_post_ID' value='1202' id='comment_post_ID' />
									<input type='hidden' name='comment_parent' id='comment_parent' value='0' />
									<p style="display: none;"><input type="hidden" id="akismet_comment_nonce" name="akismet_comment_nonce" value="632104fca1" /></p>
									<p style="display: none;"><input type="hidden" id="ak_js" name="ak_js" value="87" /></p>
									<div class="comment-form-info">
										<div class="real-time-gravatar"> <img id="real-time-gravatar" src="/statics/images/d41d8cd98f00b204e9800998ecf8427e.png" alt="gravatar头像" />
										</div>
										<p class="input-row">
											<i class="row-icon"></i>
											<textarea class="text_area" rows="3" cols="80" name="leaveComments" id="leaveComments" tabindex="4" placeholder="你不说两句吗？(°∀°)ﾉ……"></textarea>

										</p>
									</div>
									<div>
										<button onclick="f()"  type="button"  class="button" id="submit" tabindex="5"   style="display:block;margin:0 auto">发送</button>
									</div>

									<!--<input onclick="f()"  type="button"  class="button" id="submit" tabindex="5" value="发送" />-->
								</form>
							</div>
						</div>
					</section>
				</div>
			</div>
			<svg id="dwTriangleColor" width="100%" height="40" viewBox="0 0 100 102" preserveAspectRatio="none">
				<path d="M0 0 L50 100 L100 0 Z"></path>
			</svg>
			<div class="wrapper">
			</div>
		</div>

		<!--<div class="p-header">-->
			<!--<figure class="p-image" style="background-image: url(/statics/images/47fb3c_9afed6c259f94589881bd55376206366mv2_d_3840_5784_s_4_2.jpg);"></figure>-->
		<!--</div>-->
		<!--<div class="navpost-part">-->
			<!--<div id="NextPrevPosts">-->
				<!--<div class="prev" data-aos="slide-right" data-aos-delay="1.5s">-->
					<!--<div class="arrow"><i class="iconfont">&#xe625;</i></div>-->
					<!--<div class="preview">-->
						<!--<div class="pull-left featuredImg" style="background-image:url('/statics/images/97a354cb9519f97e84139f26017386026b3fd7f517b96-DCDbdE-250x250.jpeg');"></div>-->
						<!--<a class="pull-left preview-content bold" href="#"><span>《别哭妈妈》</span></a>-->
					<!--</div>-->
				<!--</div>-->
				<!--<div class="next" data-aos="slide-left" data-aos-delay="1.5s">-->
					<!--<div class="arrow"><i class="iconfont">&#xe623;</i></div>-->
					<!--<div class="preview">-->
						<!--<div class="pull-right featuredImg" style="background-image:url('/statics/images/no-image.png');"></div>-->
						<!--<a class="pull-right preview-content bold" href="#"><span>重构图像样式测试</span></a>-->
					<!--</div>-->
				<!--</div>-->
			<!--</div>-->
		<!--</div>-->

		<div th:replace="before/title::tail" ></div>
		<script th:inline="javascript">
			//显示文字内容
			var content=[[${ article.content}]];
            document.getElementById("cont").innerHTML= content;

            function f() {
                var _data={
                    'articleId':  $("#articleId").val(),
                    'leaveComments': $("#leaveComments").val()
                };
                $.ajax({
                    url: '/tourist/add.html',
                    type:"post",
                    data:JSON.stringify(_data),
                    contentType:"application/json;charset=UTF-8",
                    dataType:'json',
                    success: function (result) {
                        if (result.code === 0) {
							window.alert('留言成功');
                            location.reload();
                        } else if (result.code === 2) {
							window.location.href=result.data;
                        }else {
                            window.alert('留言失败');
                            location.reload();
						}
                    }
                })
            }

		</script>
	</body>

</html>