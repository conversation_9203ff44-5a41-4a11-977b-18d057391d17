<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.jianli.mapper.WorkMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.jianli.object.WorkDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="job" jdbcType="VARCHAR" property="job" />
    <result column="work_content" jdbcType="VARCHAR" property="workContent" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="company_img" jdbcType="VARCHAR" property="companyImg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_name, job, work_content, start_time, end_time, company_img
  </sql>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.jianli.object.WorkExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jianli_work
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from jianli_work
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.jianli.object.WorkExample">
    delete from jianli_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.jianli.object.WorkDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_work (company_name, job, work_content, 
      start_time, end_time, company_img
      )
    values (#{companyName,jdbcType=VARCHAR}, #{job,jdbcType=VARCHAR}, #{workContent,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, #{companyImg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.jianli.object.WorkDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_work
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        company_name,
      </if>
      <if test="job != null">
        job,
      </if>
      <if test="workContent != null">
        work_content,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="companyImg != null">
        company_img,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        #{job,jdbcType=VARCHAR},
      </if>
      <if test="workContent != null">
        #{workContent,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
      <if test="companyImg != null">
        #{companyImg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.jianli.object.WorkExample" resultType="java.lang.Long">
    select count(*) from jianli_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jianli_work
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.job != null">
        job = #{record.job,jdbcType=VARCHAR},
      </if>
      <if test="record.workContent != null">
        work_content = #{record.workContent,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=DATE},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=DATE},
      </if>
      <if test="record.companyImg != null">
        company_img = #{record.companyImg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jianli_work
    set id = #{record.id,jdbcType=INTEGER},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      job = #{record.job,jdbcType=VARCHAR},
      work_content = #{record.workContent,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=DATE},
      end_time = #{record.endTime,jdbcType=DATE},
      company_img = #{record.companyImg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.jianli.object.WorkDO">
    update jianli_work
    <set>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        job = #{job,jdbcType=VARCHAR},
      </if>
      <if test="workContent != null">
        work_content = #{workContent,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=DATE},
      </if>
      <if test="companyImg != null">
        company_img = #{companyImg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.jianli.object.WorkDO">
    update jianli_work
    set company_name = #{companyName,jdbcType=VARCHAR},
      job = #{job,jdbcType=VARCHAR},
      work_content = #{workContent,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=DATE},
      end_time = #{endTime,jdbcType=DATE},
      company_img = #{companyImg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.jianli.object.WorkExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_work
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>