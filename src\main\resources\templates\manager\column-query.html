<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  
  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/layui/layui.js"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  
  <body>
    <div class="x-body">
        <form class="layui-form">
            <input type="hidden"  th:value="${ column.id}" id="id"  name="id"  autocomplete="off" class="layui-input">
            <input type="hidden"  th:value="${ column.parentId}" id="pId"  name="pId"  autocomplete="off" class="layui-input">
            <div class="layui-form-item">
                <label for="parentId" class="layui-form-label">
                    <span class="x-red">*</span>父节点
                </label>
                <div class="layui-input-inline">
                    <select  name="parentId" id="parentId"  lay-search="">
                        <option value="">直接选择或搜索选择</option>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    <span class="x-red">*</span> 选择父接口，不选则本身为父节点
                </div>
            </div>
            <div class="layui-form-item">
                <label for="name" class="layui-form-label">
                    <span class="x-red">*</span>栏目名称
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ column.name}" type="text" id="name" name="name" required="" lay-verify="name"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="link" class="layui-form-label">
                    <span class="x-red">*</span>栏目链接
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ column.link}" type="text" id="link" name="link" required="" lay-verify="link"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="description" class="layui-form-label">
                    <span class="x-red">*</span>栏目描述
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ column.description}" type="text" id="description" name="description" required="" lay-verify="description"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label  class="layui-form-label">
                </label>
                <button  class="layui-btn" lay-filter="add" lay-submit="">
                    确认修改
                </button>
            </div>
        </form>
    </div>
    <script >
        layui.use(['form','layer'], function(){
          var  $ = layui.jquery;
          var form = layui.form
          ,layer = layui.layer;

          //自定义验证规则
          form.verify({
            nikename: function(value){
              if(value.length < 5){
                return '昵称至少得5个字符啊';
              }
            }
            ,pass: [/(.+){6,12}$/, '密码必须6到12位']
            ,repass: function(value){
                if($('#L_pass').val()!=$('#L_repass').val()){
                    return '两次密码不一致';
                }
            }
          });

            //监听提交
            form.on('submit(add)', function(data){
                $.ajax({
                    type: "POST",  //提交方式
                    url: "/manager/column/edit.html",
                    dataType: 'json',
                    async: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data.field),
                    success: function (result) {//返回数据根据结果进行相应的处理
                        if (result.code === 0) {
                            //发异步，把数据提交给php
                            layer.alert("修改成功", {icon: 6},function () {
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                //刷新父页面
                                window.parent.location.reload();
                            });
                        } else {
                            layer.alert(result.msg);
                        }
                    }
                });
                return false;
            });

            $(document).ready(function(){
                $.ajax({
                    type: "GET",  //提交方式
                    url: "/manager/column/queryParentColumn.html?id="+$("#id").val(),//路径
                    dataType : 'json',
                    contentType : 'application/json',
                    success: function (result) {
                        if(!result.success){layer.alert(result.msg,{icon: 2,skin: 'layer-ext-moon'});return;}
                        var list = result.data;
                        if (list == null||list.length==0) return;
                        var opts = "";
                        for (var i = 0; i < list.length; i++) {
                            opts += "<option   value='" + list[i].id + "'>" + list[i].name + "</option>";
                        }

                        $("select[name='parentId']").append(opts);
                        var parenId = $("#pId").val();
                        $("#parentId").find("option[value = '"+ parenId+"']").attr("selected","selected");
                        form.render('select');
                    }
                });
            });

        });
    </script>
  </body>

</html>