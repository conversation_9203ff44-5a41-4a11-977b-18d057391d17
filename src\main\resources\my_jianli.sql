/*
Navicat MySQL Data Transfer

Source Server         : 腾讯云
Source Server Version : 50728
Source Host           : **************:3306
Source Database       : my_jianli

Target Server Type    : MYSQL
Target Server Version : 50728
File Encoding         : 65001

Date: 2019-11-21 16:25:24
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for boke_article
-- ----------------------------
DROP TABLE IF EXISTS `boke_article`;
CREATE TABLE `boke_article` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL COMMENT '文章主题',
  `content` text COMMENT '文章内容',
  `creat_time` datetime DEFAULT NULL,
  `user_id` int(12) DEFAULT NULL,
  `description` varchar(50) DEFAULT NULL COMMENT '文章描述',
  `column_id` int(12) DEFAULT NULL COMMENT '关联栏目id',
  `tourist_id` int(12) DEFAULT NULL COMMENT '关联游客id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of boke_article
-- ----------------------------
INSERT INTO `boke_article` VALUES ('41', 'windos server下编写.bat文件运行spirngboot项目 jar包', '<p>因为是boot用的内嵌的tomcat所以，你只需安装jdk和mysql就行了。（安装步骤省略，不懂的可以留言问我）</p><p>进入正题：</p><p><span>1，编写启动文件。</span></p><p>&nbsp; &nbsp; 新建一个.txt文件，加入如下内容</p><pre class=\"has\" name=\"code\"><code class=\"hljs sql\"><ol class=\"hljs-ln\"><li><div class=\"hljs-ln-numbers\"><div class=\"hljs-ln-line hljs-ln-n\" data-line-number=\"1\" style=\"text-align: right;\"></div></div><div class=\"hljs-ln-code\"><div class=\"hljs-ln-line\">@echo off</div></div></li><li><div class=\"hljs-ln-numbers\"><div class=\"hljs-ln-line hljs-ln-n\" data-line-number=\"2\" style=\"text-align: right;\"></div></div><div class=\"hljs-ln-code\"><div class=\"hljs-ln-line\"><span class=\"hljs-keyword\">set</span> <span class=\"hljs-keyword\">path</span>=C:\\Program Files\\<span class=\"hljs-keyword\">Java</span>\\jre1<span class=\"hljs-number\">.8</span><span class=\"hljs-number\">.0</span>_162\\<span class=\"hljs-keyword\">bin</span></div></div></li><li><div class=\"hljs-ln-numbers\"><div class=\"hljs-ln-line hljs-ln-n\" data-line-number=\"3\" style=\"text-align: right;\"></div></div><div class=\"hljs-ln-code\"><div class=\"hljs-ln-line\"><span class=\"hljs-keyword\">START</span> <span class=\"hljs-string\">\"shop-project\"</span> <span class=\"hljs-string\">\"%path%\\javaw\"</span> -jar shop.jar</div></div></li><li><div class=\"hljs-ln-numbers\"><div class=\"hljs-ln-line hljs-ln-n\" data-line-number=\"4\" style=\"text-align: right;\"></div></div><div class=\"hljs-ln-code\"><div class=\"hljs-ln-line\">pause</div></div></li></ol></code></pre><p><span>注意：</span>&nbsp; （1）path=后面填写你自己的jdk下的jre的bin目录路径。</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（2）shop.jar 为你的 jar包名称。</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（3）pause&nbsp; 如果不加的话，之后点击启动文件不会有黑窗口出现。加了之后会有一个黑窗口，输入任意键就会消失。</p><p>&nbsp;</p><p><span>2，把这个.txt文件改成.bat文件</span>。</p><p><span>3，点击这个.bat文件，启动项目。</span></p><p><span>4，编写关闭进程文件</span></p><p>&nbsp; &nbsp; &nbsp; &nbsp;新建.txt文件，加入如下内容进行关闭：</p><pre class=\"has\" name=\"code\"><code class=\"hljs css\"><span class=\"hljs-selector-tag\">taskkill</span> <span class=\"hljs-selector-tag\">-f</span> <span class=\"hljs-selector-tag\">-t</span> <span class=\"hljs-selector-tag\">-im</span> <span class=\"hljs-selector-tag\">javaw</span><span class=\"hljs-selector-class\">.exe</span></code></pre><p><span>5，把这个.txt文件改成.bat文件。</span></p><p><span>6,&nbsp; 点击这个.bat文件，关闭进程。</span></p>', '2019-04-01 10:35:02', null, '编写.bat文件启动java项目', '30', null);
INSERT INTO `boke_article` VALUES ('42', 'java爬取网页模拟表单提交后的数据并进行处理', '<p>最近在做一个关于数据爬取的功能，在此记录一下。（该功能主要是模拟表单提交后获取提交后的页面的数据，我们再进行处理）</p><p><br></p><p>本文章用（http://www.suanmingde.com/xingming/xmpd/）网站作为例子，如有侵权等请联系我删除。</p><p><br></p><p>&nbsp; &nbsp; 下面帖代码：</p><p><br></p><p>&nbsp; &nbsp; &nbsp;</p><p>&nbsp; &nbsp; String name1=\"王也\";</p><p>&nbsp; &nbsp; String name2=\"诸葛青\";</p><p>&nbsp; &nbsp; Integer xing1=0;</p><p>&nbsp; &nbsp; Integer xing2=0;</p><p>&nbsp; &nbsp; Integer sex1=0;</p><p>&nbsp; &nbsp; Integer sex2=0;</p><p>&nbsp;</p><p>&nbsp; &nbsp; // 得到浏览器对象，直接New一个就能得到，现在就好比说你得到了一个浏览器了</p><p>&nbsp; &nbsp; WebClient webclient = new WebClient();</p><p>&nbsp;</p><p>// 这里是配置一下不加载css和javaScript,配置起来很简单，是不是</p><p>&nbsp; &nbsp; &nbsp; &nbsp; webclient.getOptions().setCssEnabled(false);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; webclient.getOptions().setJavaScriptEnabled(false);</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; // 做的第一件事，去拿到这个网页，只需要调用getPage这个方法即可</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; HtmlPage htmlpage = webclient.getPage(\"http://www.suanmingde.com/xingming/xmpd/\");</p><p>// 根据名字得到一个表单，查看上面这个网页的源代码可以发现表单的名字叫“f”</p><p>//final HtmlForm form = htmlpage.getFormByName(\"f\");</p><p>//得到网页上第二个表单</p><p>final HtmlForm form = htmlpage.getForms().get(1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获取网页的配对按钮 （根据class）</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlElement button = htmlpage.getHtmlElementById(\"btn_begincs2\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; // 得到搜索框</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlTextInput txtUName = form.getInputByName(\"name1\"); //用户名text框</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //设置值</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUName.setValueAttribute(name1);</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlSelect txtUName1 = form.getSelectByName(\"xing1\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUName1.setSelectedIndex(xing1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlSelect&nbsp; txtUName2 = form.getSelectByName(\"sex1\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUName2.setSelectedIndex(sex1);</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlTextInput&nbsp; txtUNameq = (HtmlTextInput )form.getInputByName(\"name2\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUNameq.setValueAttribute(name2);</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlSelect txtUNameg1 = form.getSelectByName(\"xing2\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUNameg1.setSelectedIndex(xing2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; HtmlSelect&nbsp; txtUNameg2 = form.getSelectByName(\"sex2\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; txtUNameg2.setSelectedIndex(sex2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //提交表单</p><p>&nbsp; &nbsp; &nbsp; &nbsp; final HtmlPage nextPage = button.click();</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //把获得后的网页转换成document</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Document document = Jsoup.parse(nextPage.asXml());</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获得第一人的 五行和五格</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Map&lt;String, String&gt; stringStringMap = haveAttribute(document, 1,name1.length());</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获得第二人的 五行和五格</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Map&lt;String, String&gt; stringStringMap2 = haveAttribute(document, 2,name2.length());</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获得配对文本结果</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Element e = document.select(\"[class=\\\"box_con\\\"]\").get(2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Elements font = e.select(\"font\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String resulText = font.text().toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Elements span = e.select(\"span\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String resultPairZong = span.text().toString().split(\"分\")[0];</p><p><br></p><p><br></p><p>&nbsp;上面我抽取了一个方法</p><p>haveAttribute(document, 1,name1.length())</p><p>参数说明&nbsp; ：&nbsp;&nbsp;</p><p><br></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;第一个参数表示 获取后的页面的decument形式。</p><p><br></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;第二个参数表示&nbsp; 获取第几个class为clearfix的对象</p><p><br></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;第三个参数表示&nbsp; 姓名的长度</p><p><br></p><p><br></p><p><br></p><p>上面的方法根据自己的实际业务进行处理。</p><p><br></p><p><br></p><p><br></p><p>下面贴上抽取的方法代码：</p><p><br></p><p>&nbsp;//获取网页数据 五行和五格&nbsp; num等于1时 获得第一个人的&nbsp; &nbsp;num等于2时&nbsp; 获得第二人的</p><p>&nbsp; &nbsp; public static Map&lt;String,String&gt; haveAttribute(Document document, Integer num,Integer nameLeng){</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //保存到map</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Map&lt;String, String&gt; map = new HashMap&lt;&gt;();</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获得name的五行</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Element e = document.select(\"[class=\\\"clearfix\\\"]\").get(num);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Elements li = e.select(\"li\");</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //判断姓名的长度</p><p>&nbsp; &nbsp; &nbsp; &nbsp; if (nameLeng==1){</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第一个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute1 = li.get(9).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute1\",attribute1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; }</p><p>&nbsp; &nbsp; &nbsp; &nbsp; if (nameLeng==2){</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第一个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute1 = li.get(9).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第二个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute2 = li.get(14).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute1\",attribute1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute2\",attribute2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; }</p><p>&nbsp; &nbsp; &nbsp; &nbsp; if (nameLeng==3){</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第一个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute1 = li.get(9).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第二个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute2 = li.get(14).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第三个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute3 = li.get(19).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute1\",attribute1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute2\",attribute2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute3\",attribute3);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; }</p><p>&nbsp; &nbsp; &nbsp; &nbsp; if (nameLeng==4){</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第一个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute1 = li.get(9).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第二个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute2 = li.get(14).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第三个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute3 = li.get(19).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //获得第三个五行的属性</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; String attribute4 = li.get(24).text();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute1\",attribute1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute2\",attribute2);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute3\",attribute3);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; map.put(\"attribute4\",attribute4);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; }</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; //获得name1的五格</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Element el = document.select(\"[class=\\\"sort_2nd\\\"]\").get(num-1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String text = el.text().toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String[] split = text.split(\" \");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String tian = split[2].toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String ren = split[6].toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String s = split[9].toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String[] split1 = s.split(\"&gt;&gt;\");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String di = split1[1].toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; Element els = document.select(\"[class=\\\"sort_3rd\\\"]\").get(num-1);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String text2 = els.text().toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String[] split2 = text2.split(\" \");</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String wai = split2[1].split(\"&gt;&gt;\")[1].toString();</p><p>&nbsp; &nbsp; &nbsp; &nbsp; String zong = split2[5].toString();</p><p>&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; map.put(\"tian\",tian);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; map.put(\"ren\",ren);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; map.put(\"di\",di);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; map.put(\"wai\",wai);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; map.put(\"zong\",zong);</p><p>&nbsp; &nbsp; &nbsp; &nbsp; return map;</p><p>&nbsp; &nbsp; }</p><p><br></p><p>&nbsp; 导入的依赖：</p><p><br></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&lt;dependency&gt;</p><p><span>			</span>&lt;groupId&gt;org.jsoup&lt;/groupId&gt;</p><p><span>			</span>&lt;artifactId&gt;jsoup&lt;/artifactId&gt;</p><p><span>			</span>&lt;version&gt;1.11.2&lt;/version&gt;</p><p><span>		</span>&lt;/dependency&gt;</p><p><span>		</span>&lt;dependency&gt;</p><p><span>			</span>&lt;groupId&gt;net.sourceforge.htmlunit&lt;/groupId&gt;</p><p><span>			</span>&lt;artifactId&gt;htmlunit&lt;/artifactId&gt;</p><p><span>			</span>&lt;version&gt;2.18&lt;/version&gt;</p><p><span>		</span>&lt;/dependency&gt;</p><p><br></p><p>over！</p><p><br></p><p><br></p><p>---------------------&nbsp;</p><p>作者：_benz&nbsp;</p><p>来源：CSDN&nbsp;</p><p>原文：https://blog.csdn.net/weixin_41541415/article/details/80278653&nbsp;</p><p>版权声明：本文为博主原创文章，转载请附上博文链接！</p>', '2019-11-20 17:58:53', null, 'java爬取数据', '29', null);
INSERT INTO `boke_article` VALUES ('43', 'mysql报错sql_mode=only_full_group_by', '<p>记一次，在迁移服务的时候，连上新的数据库，数据库也是备份的以前的，发现报错：</p><pre class=\"has\" name=\"code\"><code class=\"hljs delphi\">Expression <span class=\"hljs-string\">#1</span> <span class=\"hljs-keyword\">of</span> SELECT list <span class=\"hljs-keyword\">is</span> <span class=\"hljs-keyword\">not</span> <span class=\"hljs-keyword\">in</span> GROUP BY clause <span class=\"hljs-keyword\">and</span> contains nonaggregated column <span class=\"hljs-string\">\'datacn_store.sod.goods_name\'</span> which <span class=\"hljs-keyword\">is</span> <span class=\"hljs-keyword\">not</span> functionally dependent <span class=\"hljs-keyword\">on</span> columns <span class=\"hljs-keyword\">in</span> GROUP BY clause; this <span class=\"hljs-keyword\">is</span> incompatible <span class=\"hljs-keyword\">with</span> sql_mode=only_full_group_by</code></pre><p>&nbsp;</p><p>开始以为是导入的时候编码什么的不对，后来网上查资料发现是mysql版本问题，现记录一下：</p><p>MySQL 5.7.5后only_full_group_by成为sql_mode的默认选项之一，这可能导致一些sql语句失效。</p><p>&nbsp;</p><h2 id=\"autoid-0-0-0\">解决方法</h2><ol><li><p>把group by字段<code>group_id</code>设成primary key 或者 unique NOT NULL。这个方法在实际操作中没什么意义。</p></li><li><p>使用函数<code>any_value</code>把报错的字段<code>name</code>包含起来。如，<code>select any_value(name), group_id from game group by group_id</code>。</p></li></ol>', '2018-12-01 10:51:03', null, 'mysql版本不一致，导致部分sql执行报错', '30', null);
INSERT INTO `boke_article` VALUES ('44', 'jenkins2.121.1构建java项目环境，一键打包发布', '<p>该版本为2.121.1，其他版本会稍有不同，仅做参考</p><p>1.登录jenkins后，点击左上角“新建任务”。</p><p>2.填写任务名称，然后选择构建类型（一般是选择构建一个maven项目）</p><p>3.填写General模块下信息&nbsp;</p><p>&nbsp; &nbsp; &nbsp; （1).填写描述</p><p>&nbsp; &nbsp; &nbsp; &nbsp; (2).选择&nbsp; 丢弃旧的构建</p><p>&nbsp;4.填写源码管理模块下信息</p><p>&nbsp; &nbsp; &nbsp;（1).选择git或Subversion，看自己项目用的git或者svn (下面是svn的配置)</p><p>&nbsp; &nbsp; &nbsp; &nbsp;(2).Repository URL&nbsp; :&nbsp; 项目svn的路径</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Credentials ： 如果有就随便选一个，没有就创建一个</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 其他的不用管，默认的就可以了</p><p>&nbsp; 5.填写Build模块下信息</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Root POM ：pom.xml</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Goals and options ：clean install</p><p>&nbsp; 6.填写Post Steps模块下信息</p><p>&nbsp; &nbsp; &nbsp; &nbsp;(1).选择&nbsp;Run regardless of build result选项。</p><p>&nbsp; &nbsp; &nbsp; &nbsp;(2). 点击add post-build step，选择send files execute commands over SSH</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Name : 发布的项目所在的服务器地址</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Source files&nbsp;：shop-web/target/*.jar</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;Remove prefix ：shop-web/target</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;Remote directory ：/new-shop/</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Exec command ：chmod 777 /opt/new-shop/*.jar</p><p>&nbsp; &nbsp; &nbsp; &nbsp;(3).点击add server</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Name : 发布的项目所在的服务器地址</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;Source files:&nbsp; sh/node-*.sh</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;Remove prefix :&nbsp;sh</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;Remote directory :&nbsp;/new-shop/</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Exec command :&nbsp;&nbsp;chmod 777 /opt/new-shop/*.sh</p><p>&nbsp; &nbsp; &nbsp; (4). 点击 add post-build step,选择 execute shell script on remote host using ssh</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;SSH site : root@服务器地址:22</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Command&nbsp; :/opt/new-shop/node-app.sh stop<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; cd /opt/new-shop/<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; BUILD_ID=dontKillMe ;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; nohup /opt/new-shop/node-app.sh start&gt;nohup.out &amp;</p><p><br></p><p>&nbsp; &nbsp;7.点击保存</p><p>&nbsp; &nbsp; 然后再服务器里面new-shop（这个目录是存放项目jar包的目录）目录下创建node-app.sh文件，文件内容如下：</p><p>&nbsp; &nbsp;</p><p></p><div tabindex=\"-1\" contenteditable=\"false\" data-cke-widget-wrapper=\"1\" data-cke-filter=\"off\" class=\"cke_widget_wrapper cke_widget_block cke_widget_codeSnippet cke_widget_wrapper_has cke_widget_selected\" data-cke-display-name=\"代码段\" data-cke-widget-id=\"0\" role=\"region\" aria-label=\"代码段 小部件\"><pre class=\"has cke_widget_element\" data-cke-widget-data=\"{&amp;quot;code&amp;quot;:&amp;quot;#!/bin/sh\\n# 函数名\\nf=$1\\n# get basepath in parament\\ncurrent=$(dirname $0)\\ncd ${current}\\n# 获取应用全路径\\nfunction get_full_app_path(){\\n        # statememnt app deployment path \\n        app_deploy_path=$1\\n        max_time=0\\n        max_time_file=\\&amp;quot;\\&amp;quot;\\n        for FILE in $app_deploy_path/*.jar\\n        do\\n                modify_date=`date +%s -r ${FILE}`\\n                if test $[$modify_date] -gt $[$max_time]\\n                then\\n                        max_time=$modify_date\\n                        max_time_file=$FILE\\n                fi\\n        done\\n        echo \\&amp;quot;$max_time_file\\&amp;quot;\\n\\n}\\n# 获取应用名称\\nfunction get_app(){\\n        # get app path\\n        path=$1\\n        app_path=$(get_full_app_path ${path})\\n        echo \\&amp;quot;${app_path##*/}\\&amp;quot;\\n}\\nfunction stoped(){\\n        # incoming directory\\n        app_directoy=$1\\n        # get app path\\n        app=$(get_app $app_directoy)\\n        if [ -z \\&amp;quot;${app}\\&amp;quot; ]; then\\n                echo \\&amp;quot;no find app $app\\&amp;quot;\\n        else\\n                pid=`ps -ef | grep ${app} | grep -v grep | awk \'{print $2}\'`\\n                if [ -n \\&amp;quot;${pid}\\&amp;quot; ]; then\\n                        kill -9 $pid\\n                        echo \\&amp;quot;stop OK\\&amp;quot;\\n                else\\n                        echo \\&amp;quot;haven\'t app run\\&amp;quot;\\n                fi\\n        fi\\n}\\nfunction started(){\\n        . /etc/profile\\n        # incoming direcory\\n        app_directoy=$1\\n        #get app path\\n        app_path=$(get_full_app_path ${app_directoy})\\n        echo ${app_path}\\n        chmod 777 ${app_path}\\n        ## JAVA_HOME/java ...\\n        nohup /usr/local/services/jdk1.8.0_171/bin/java -Xms512m -Xmx512m -jar ${app_path} --spring.profiles.active=stest start &amp;amp;\\n}\\n\\ncase $f in\\n    start)\\n        echo \\&amp;quot;starting app\\&amp;quot;\\n        started ${current}\\n        ;;\\n    stop)\\n        echo \\&amp;quot;stoping app\\&amp;quot;\\n        stoped ${current}\\n        ;;\\n    restart)\\n        stoped ${current}\\n        started ${current}\\n        echo \\&amp;quot;restart\\&amp;quot;\\n        ;;\\n    *)\\n        echo \\&amp;quot;start [Release directory],stop [Release directory],restart [Release directory], deploy [Release directory] [project]\\&amp;quot;\\n        ;;\\nesac\\n&amp;quot;,&amp;quot;classes&amp;quot;:{&amp;quot;has&amp;quot;:1}}\" data-cke-widget-upcasted=\"1\" data-cke-widget-keep-attr=\"0\" data-widget=\"codeSnippet\"><code class=\"hljs\">#!/bin/sh\n# 函数名\nf=$1\n# get basepath in parament\ncurrent=$(dirname $0)\ncd ${current}\n# 获取应用全路径\nfunction get_full_app_path(){\n        # statememnt app deployment path \n        app_deploy_path=$1\n        max_time=0\n        max_time_file=\"\"\n        for FILE in $app_deploy_path/*.jar\n        do\n                modify_date=`date +%s -r ${FILE}`\n                if test $[$modify_date] -gt $[$max_time]\n                then\n                        max_time=$modify_date\n                        max_time_file=$FILE\n                fi\n        done\n        echo \"$max_time_file\"\n\n}\n# 获取应用名称\nfunction get_app(){\n        # get app path\n        path=$1\n        app_path=$(get_full_app_path ${path})\n        echo \"${app_path##*/}\"\n}\nfunction stoped(){\n        # incoming directory\n        app_directoy=$1\n        # get app path\n        app=$(get_app $app_directoy)\n        if [ -z \"${app}\" ]; then\n                echo \"no find app $app\"\n        else\n                pid=`ps -ef | grep ${app} | grep -v grep | awk \'{print $2}\'`\n                if [ -n \"${pid}\" ]; then\n                        kill -9 $pid\n                        echo \"stop OK\"\n                else\n                        echo \"haven\'t app run\"\n                fi\n        fi\n}\nfunction started(){\n        . /etc/profile\n        # incoming direcory\n        app_directoy=$1\n        #get app path\n        app_path=$(get_full_app_path ${app_directoy})\n        echo ${app_path}\n        chmod 777 ${app_path}\n        ## JAVA_HOME/java ...\n        nohup /usr/local/services/jdk1.8.0_171/bin/java -Xms512m -Xmx512m -jar ${app_path} --spring.profiles.active=stest start &amp;\n}\n\ncase $f in\n    start)\n        echo \"starting app\"\n        started ${current}\n        ;;\n    stop)\n        echo \"stoping app\"\n        stoped ${current}\n        ;;\n    restart)\n        stoped ${current}\n        started ${current}\n        echo \"restart\"\n        ;;\n    *)\n        echo \"start [Release directory],stop [Release directory],restart [Release directory], deploy [Release directory] [project]\"\n        ;;\nesac\n</code></pre><span class=\"cke_reset cke_widget_drag_handler_container\"><img class=\"cke_reset cke_widget_drag_handler\" data-cke-widget-drag-handler=\"1\" src=\"data:image/gif;base64,R0lGODlhAQABAPABAP///wAAACH5BAEKAAAALAAAAAABAAEAAAICRAEAOw==\" width=\"15\" title=\"点击并拖拽以移动\" height=\"15\" role=\"presentation\"></span></div><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 需要修改&nbsp;nohup /usr/local/services/jdk1.8.0_171/bin/java 改成自己服务器的jdk路径</p><p><br></p><p><br data-cke-eol=\"1\"></p>', '2019-05-08 10:54:36', null, 'jenkins2.121.1一键自动化打包部署', '30', null);
INSERT INTO `boke_article` VALUES ('45', 'Linux中编写springboot项目启动脚本', '<p>（原理：直接把启动项目设置成一个服务 然后再启动或停止服务 就表示启动或停止项目了）</p><p><strong>1，进入/usr/local/bin目录 ，创建.sh文件（如 life_web.sh）。</strong></p><p><strong>2，在life_web.sh 文件中加入以下内容：</strong></p><p></p><div tabindex=\"-1\" contenteditable=\"false\" data-cke-widget-wrapper=\"1\" data-cke-filter=\"off\" class=\"cke_widget_wrapper cke_widget_block cke_widget_codeSnippet cke_widget_wrapper_has cke_widget_selected\" data-cke-display-name=\"代码段\" data-cke-widget-id=\"1\" role=\"region\" aria-label=\"代码段 小部件\"><pre class=\"has cke_widget_element\" data-cke-widget-data=\"{&amp;quot;code&amp;quot;:&amp;quot;#!/bin/sh\\nSERVICE_NAME=SCOGlobal_Official\\nPATH_TO_JAR=/usr/local/life-web/life-web-1.0.jar\\nPID_PATH_NAME=/tmp/life_web.pid\\n&nbsp;\\ncase $1 in\\n&nbsp; &nbsp; start)\\n&nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;Starting $SERVICE_NAME ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; if [ ! -f $PID_PATH_NAME ]; then\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; nohup java -jar $PATH_TO_JAR --server.port=8083 --spring.profiles.active=online &amp;gt;&amp;gt; /var/log/life_web.log 2&amp;gt;&amp;amp;1 &amp;amp;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo $! &amp;gt; $PID_PATH_NAME\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME started ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; else\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME is already running ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; fi\\n&nbsp; &nbsp; ;;\\n&nbsp; &nbsp; stop)\\n&nbsp; &nbsp; &nbsp; &nbsp; if [ -f $PID_PATH_NAME ]; then\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; PID=$(cat $PID_PATH_NAME);\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME stoping ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; kill $PID;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME stopped ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; rm $PID_PATH_NAME\\n&nbsp; &nbsp; &nbsp; &nbsp; else\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME is not running ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; fi\\n&nbsp; &nbsp; ;;\\n&nbsp; &nbsp; restart)\\n&nbsp; &nbsp; &nbsp; &nbsp; if [ -f $PID_PATH_NAME ]; then\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; PID=$(cat $PID_PATH_NAME);\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME stopping ...\\&amp;quot;;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; kill $PID;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME stopped ...\\&amp;quot;;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; rm $PID_PATH_NAME\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME starting ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; nohup java -jar $PATH_TO_JAR --server.port=8083 --spring.profiles.active=online &amp;gt;&amp;gt; /var/log/life_web.log 2&amp;gt;&amp;amp;1 &amp;amp;\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo $! &amp;gt; $PID_PATH_NAME\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME started ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; else\\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \\&amp;quot;$SERVICE_NAME is not running ...\\&amp;quot;\\n&nbsp; &nbsp; &nbsp; &nbsp; fi\\n&nbsp; &nbsp; ;;\\nesac&amp;quot;,&amp;quot;classes&amp;quot;:{&amp;quot;has&amp;quot;:1}}\" data-cke-widget-upcasted=\"1\" data-cke-widget-keep-attr=\"0\" data-widget=\"codeSnippet\"><code class=\"hljs\">#!/bin/sh\nSERVICE_NAME=SCOGlobal_Official\nPATH_TO_JAR=/usr/local/life-web/life-web-1.0.jar\nPID_PATH_NAME=/tmp/life_web.pid\n&nbsp;\ncase $1 in\n&nbsp; &nbsp; start)\n&nbsp; &nbsp; &nbsp; &nbsp; echo \"Starting $SERVICE_NAME ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; if [ ! -f $PID_PATH_NAME ]; then\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; nohup java -jar $PATH_TO_JAR --server.port=8083 --spring.profiles.active=online &gt;&gt; /var/log/life_web.log 2&gt;&amp;1 &amp;\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo $! &gt; $PID_PATH_NAME\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME started ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; else\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME is already running ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; fi\n&nbsp; &nbsp; ;;\n&nbsp; &nbsp; stop)\n&nbsp; &nbsp; &nbsp; &nbsp; if [ -f $PID_PATH_NAME ]; then\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; PID=$(cat $PID_PATH_NAME);\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME stoping ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; kill $PID;\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME stopped ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; rm $PID_PATH_NAME\n&nbsp; &nbsp; &nbsp; &nbsp; else\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME is not running ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; fi\n&nbsp; &nbsp; ;;\n&nbsp; &nbsp; restart)\n&nbsp; &nbsp; &nbsp; &nbsp; if [ -f $PID_PATH_NAME ]; then\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; PID=$(cat $PID_PATH_NAME);\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME stopping ...\";\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; kill $PID;\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME stopped ...\";\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; rm $PID_PATH_NAME\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME starting ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; nohup java -jar $PATH_TO_JAR --server.port=8083 --spring.profiles.active=online &gt;&gt; /var/log/life_web.log 2&gt;&amp;1 &amp;\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo $! &gt; $PID_PATH_NAME\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME started ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; else\n&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; echo \"$SERVICE_NAME is not running ...\"\n&nbsp; &nbsp; &nbsp; &nbsp; fi\n&nbsp; &nbsp; ;;\nesac</code></pre><span class=\"cke_reset cke_widget_drag_handler_container\"><img class=\"cke_reset cke_widget_drag_handler\" data-cke-widget-drag-handler=\"1\" src=\"data:image/gif;base64,R0lGODlhAQABAPABAP///wAAACH5BAEKAAAALAAAAAABAAEAAAICRAEAOw==\" width=\"15\" title=\"点击并拖拽以移动\" height=\"15\" role=\"presentation\"></span></div><p><br></p><p>&nbsp;<strong>注意：</strong></p><p>&nbsp; SERVICE_NAM&nbsp; ：服务名（随便取一个 最好和项目名一样）</p><p>&nbsp; SERVICE_NAM ：启动的项目jar包路径</p><p>&nbsp; PID_PATH_NAME：pid(目前不知道什么作用)</p><p>&nbsp; server.port ：启动端口号&nbsp;</p><p>&nbsp;spring.profiles.active ：选择启动的application.properties文件（如上启动的是 ： application-online.properties 文件）</p><p>&nbsp;/var/log/life_web.log ： 指定项目启动输出的日志路径</p><p><br></p><p><br></p><p><strong>3，进入&nbsp;/etc/systemd/system目录添加.service文件（如&nbsp; LifeWeb.service）这个名字是后面项目启动、停止、重启的文件名字</strong></p><p><br></p><p><strong>4，在LifeWeb.service文件中添加如下内容</strong></p><p></p><div tabindex=\"-1\" contenteditable=\"false\" data-cke-widget-wrapper=\"1\" data-cke-filter=\"off\" class=\"cke_widget_wrapper cke_widget_block cke_widget_codeSnippet cke_widget_wrapper_has cke_widget_selected\" data-cke-display-name=\"代码段\" data-cke-widget-id=\"0\" role=\"region\" aria-label=\"代码段 小部件\"><pre class=\"has cke_widget_element\" data-cke-widget-data=\"{&amp;quot;code&amp;quot;:&amp;quot;\\n[Unit]\\nDescription=SCOGlobal Life Web Service\\nAfter=network.target\\n \\n[Service]\\nType = forking\\nExecStart = /usr/local/bin/life_web.sh start\\nExecStop = /usr/local/bin/life_web.sh stop\\nExecReload = /usr/local/bin/life_web.sh restart\\n \\n[Install]\\nWantedBy=multi-user.target\\n&amp;quot;,&amp;quot;classes&amp;quot;:{&amp;quot;has&amp;quot;:1}}\" data-cke-widget-upcasted=\"1\" data-cke-widget-keep-attr=\"0\" data-widget=\"codeSnippet\"><code class=\"hljs\">\n[Unit]\nDescription=SCOGlobal Life Web Service\nAfter=network.target\n \n[Service]\nType = forking\nExecStart = /usr/local/bin/life_web.sh start\nExecStop = /usr/local/bin/life_web.sh stop\nExecReload = /usr/local/bin/life_web.sh restart\n \n[Install]\nWantedBy=multi-user.target\n</code></pre><span class=\"cke_reset cke_widget_drag_handler_container\"><img class=\"cke_reset cke_widget_drag_handler\" data-cke-widget-drag-handler=\"1\" src=\"data:image/gif;base64,R0lGODlhAQABAPABAP///wAAACH5BAEKAAAALAAAAAABAAEAAAICRAEAOw==\" width=\"15\" title=\"点击并拖拽以移动\" height=\"15\" role=\"presentation\"></span></div><p><strong>注意：</strong></p><p>&nbsp; &nbsp; &nbsp;Description ：描述（项目描述）</p><p>&nbsp; &nbsp; &nbsp;After ：直接填写如上内容即可（network.target）</p><p>&nbsp; &nbsp; Type :&nbsp;直接填写如上内容即可(forking)</p><p>&nbsp; &nbsp; ExecStart : 项目启动命令，填.sh文件的路径和start命令 （表示启动指定.sh文件下的服务）</p><p>&nbsp; &nbsp; ExecStop ：项目停止命令，填.sh文件的路径和stop命令（表示停止指定.sh文件下的服务）</p><p>&nbsp; &nbsp; ExecReload ：项目重启命令，填.sh文件的路径和restart命令（表示重启指定.sh文件下的服务）</p><p>&nbsp; &nbsp; WantedBy ：直接填写如上内容即可（ multi-user.target）</p><p><br></p><p><strong>5，服务启动命令&nbsp; </strong></p><p>&nbsp; &nbsp; systemctl start LifeWeb.service&nbsp; 或者&nbsp;&nbsp;systemctl start LifeWeb&nbsp; （LifeWeb&nbsp; 这个名字和上面的.service文件名字一致）</p><p>&nbsp; &nbsp; 服务停止命令</p><p>&nbsp; &nbsp;systemctl stop LifeWeb.service&nbsp; 或者&nbsp;&nbsp;systemctl stop LifeWeb&nbsp; （LifeWeb&nbsp; 这个名字和上面的.service文件名字一致）</p><p>&nbsp; &nbsp;服务重启命令</p><p>&nbsp; &nbsp;systemctl restart LifeWeb.service&nbsp; 或者&nbsp;&nbsp;systemctl restart LifeWeb&nbsp; （LifeWeb&nbsp; 这个名字和上面的.service文件名字一致）</p><p>&nbsp; &nbsp;</p><p>&nbsp; &nbsp;查看项目启动日志</p><p>&nbsp; &nbsp; tail -f /var/log/life_web.log</p><p>其他命令</p><p>&nbsp; &nbsp; 显示一个服务的状态：systemctl status&nbsp; LifeWeb.service<br>&nbsp;&nbsp; &nbsp;在开机时启用一个服务：systemctl enable LifeWeb.service<br>&nbsp;&nbsp; &nbsp;在开机时禁用一个服务：systemctl disable LifeWeb.service<br>&nbsp;&nbsp; &nbsp;查看服务是否开机启动：systemctl is-enabled LifeWeb.service;echo $?<br>&nbsp;&nbsp; &nbsp;查看已启动的服务列表：systemctl list-unit-files|grep enabled</p><p><br></p><p><br></p><p><br></p><p><br></p><p><br data-cke-eol=\"1\"></p>', '2019-04-02 10:56:06', null, '把java项目注册成服务启动', '30', null);
INSERT INTO `boke_article` VALUES ('46', 'Linux下安装jdk', '<div yne-bulb-block=\"paragraph\">把jdk安装到/usr/java 目录下</div><div yne-bulb-block=\"paragraph\"> </div><div yne-bulb-block=\"paragraph\">进入文件 添加内容</div><div yne-bulb-block=\"paragraph\">vim etc/profile</div><div yne-bulb-block=\"paragraph\"><br></div><div yne-bulb-block=\"paragraph\">添加</div><div yne-bulb-block=\"paragraph\">JAVA_HOME=/usr/java/jdk1.8.0_191</div><div yne-bulb-block=\"paragraph\">CLASSPATH=$JAVA_HOME/lib/</div><div yne-bulb-block=\"paragraph\">PATH=$PATH:$JAVA_HOME/bin</div><div yne-bulb-block=\"paragraph\">export PATH JAVA_HOME CLASSPATH</div><div yne-bulb-block=\"paragraph\"><br></div><div yne-bulb-block=\"paragraph\"><br></div><div yne-bulb-block=\"paragraph\">然后 <span>source  </span>etc/profile 使文件生效  </div><div yne-bulb-block=\"paragraph\"><br></div><p><!--5f39ae17-8c62-4a45-bc43-b32064c9388a: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--></p><div yne-bulb-block=\"paragraph\" style=\"text-align: left;\">java -version 查看是否配置成功</div>', '2019-03-01 11:02:37', null, 'linux安装jdk', '30', null);
INSERT INTO `boke_article` VALUES ('47', 'Linux下安装mysql', '<p><br></p><p>1. 查看有没有安装包:</p><p><span>			</span>&nbsp; &nbsp; yum list mysql*</p><p><span>			</span>&nbsp; &nbsp; #移除已经安装的mysql</p><p><span>			</span>&nbsp; &nbsp; yum remove mysql mysql-server mysql-libs compat-mysql51</p><p><span>			</span>&nbsp; &nbsp; rm -rf /var/lib/mysql</p><p><span>			</span>&nbsp; &nbsp; rm /etc/my.cnf</p><p><span>			</span>2. 查看是否还有mysql软件:</p><p><span>			</span>&nbsp; &nbsp; rpm -qa|grep mysql</p><p><span>			</span>&nbsp; &nbsp; #如果有的话,继续删除</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;rpm -ev&nbsp; mysql包名</p><p><br></p><p>安装</p><p>1. wget -i -c http://dev.mysql.com/get/mysql57-community-release-el7-10.noarch.rpm</p><p><br></p><p>2. yum -y install mysql57-community-release-el7-10.noarch.rpm</p><p><br></p><p>3.&nbsp; yum -y install mysql-community-server</p><p><br></p><p>启动</p><p>&nbsp;systemctl start&nbsp; mysqld.service</p><p>查看是否启动成功</p><p>systemctl status mysqld.service</p><p><br></p><p>查看初始密码</p><p>&nbsp; grep \"password\" /var/log/mysqld.log</p><p>&nbsp; root@localhost:后面的就是初始密码</p><p><br></p><p>进入mysql</p><p>&nbsp; mysql -uroot -p&nbsp; &nbsp; &nbsp;# 回车后会提示输入密码</p><p><br></p><p>重置密码</p><p>&nbsp;（注意 mysql初始密码有规则不能太简单&nbsp;</p><p>&nbsp; &nbsp;查看规则 ：</p><p>&nbsp; &nbsp; &nbsp; &nbsp;SHOW VARIABLES LIKE \'validate_password%\';</p><p>修改规则：</p><p>&nbsp;set global validate_password_policy=0;</p><p>&nbsp;set global validate_password_length=1;</p><p>然后执行下面的重置密码命令</p><p>）</p><p>ALTER USER \'root\'@\'localhost\' IDENTIFIED BY \'你的密码\';</p><p><br></p><p>卸载yum （可不执行）</p><p>&nbsp;因为安装了Yum Repository，以后每次yum操作都会自动更新，需要把这个卸载掉：</p><p>&nbsp; yum -y remove mysql57-community-release-el7-10.noarch</p><p><br></p><p>可视化工具的登录授权：(如果授权不成功，请查看防火墙)</p><p>操作完成上面的，现在还不能用可视化的客户端进行连接，需要我们进行授权：</p><p>grant all on *.* to root@\'%\' identified by \'数据库密码\';</p><p><br></p><p>退出mysql：&nbsp; &nbsp;exit</p>', '2019-03-12 11:04:43', null, '安装mysql', '30', null);
INSERT INTO `boke_article` VALUES ('48', 'Linux下安装Nginx', '<p>安装make：</p><p>yum -y install gcc automake autoconf libtool make</p><p>安装g++:</p><p>yum install gcc gcc-c++</p><p><br></p><p>选择安装目录，选择&nbsp; cd /usr/local/src</p><p>cd /usr/local/src</p><p><br></p><p>安装pcre库</p><p>1 cd /usr/local/src</p><p>2 wget ftp://ftp.csx.cam.ac.uk/pub/software/programming/pcre/pcre-8.39.tar.gz</p><p>3 tar -zxvf pcre-8.37.tar.gz 4 cd pcre-8.34 5 ./configure 6 make 7 make install</p><p><br></p><p>安装zlib库</p><p>1 cd /usr/local/src 2 wget http://zlib.net/zlib-1.2.11.tar.gz 3 tar -zxvf zlib-1.2.11.tar.gz 4 cd zlib-1.2.11 5 ./configure 6 make 7 make install</p><p><br></p><p>安装openssl(某些vps默认没装)</p><p>1 cd /usr/local/src 2 wget https://www.openssl.org/source/openssl-1.0.1t.tar.gz 3 tar -zxvf openssl-1.0.1t.tar.gz</p><p><br></p><p>安装nginx（下面是把nginx安装到 /usr/local/nginx目录）</p><p>1 cd /usr/local/src 2 wget http://nginx.org/download/nginx-1.1.10.tar.gz 3 tar -zxvf nginx-1.1.10.tar.gz 4 cd nginx-1.1.10</p><p>5 ./configure</p><p>6 make</p><p>7 make install</p><p><br></p><p>可能会报错 ，执行下面指令</p><p>yum -y install openssl openssl-devel</p><p><br></p><p>修改nginx默认端口号（它的默认端口号是80 和Apache端口号也是80，所以改nginx默认端口号）</p><p>vim /usr/local/nginx/conf/nginx.conf</p><p>进入文件后修改server下listen 后面的值 （进入后默认是80，修改成你要改的值如：8090）</p><p><br></p><p>验证nginx配置文件是否正确</p><p>方法一： 进入nginx安装目录sbin下，输入命令./nginx -t</p><p>看到如下显示nginx.conf syntax is ok</p><p>nginx.conf test is successful</p><p>说明配置文件正确！</p><p><br></p><p>方法二：在启动命令-c前加-t</p><p>/usr/local/nginx/sbin/nginx -t -c /usr/local/nginx/conf/nginx.conf</p><p><br></p><p>启动nginx</p><p>启动代码格式：nginx安装目录地址 -c nginx配置文件地址</p><p>/usr/local/nginx/sbin/nginx -c /usr/local/nginx/conf/nginx.conf</p><p><br></p><p>查看进程号</p><p>ps -ef|grep nginx</p><p>停止（从容停止）</p><p>kill -QUIT 端口号</p><p><br></p><p>重启nginx</p><p>进入nginx的sbin目录下，输入命令</p><p>./nginx -s reload</p>', '2019-03-05 11:05:42', null, '安装nginx', '30', null);
INSERT INTO `boke_article` VALUES ('49', '部署Git私服仓库', '<p>安装git 服务器端：</p><p>&nbsp; &nbsp; &nbsp; &nbsp; yum install -y git</p><p><br></p><p>安装完后，查看 Git 版本</p><p>&nbsp; &nbsp; &nbsp; &nbsp; git --version</p><p><br></p><p>客户端：</p><p>&nbsp; &nbsp;地址：https://git-for-windows.github.io/</p><p><br></p><p>安装完之后，查看 Git 版本&nbsp; &nbsp;</p><p>&nbsp; &nbsp; &nbsp; git --version</p><p>&nbsp; &nbsp;</p><p>服务器端创建 git 用户，用来管理 Git 服务，并为 git 用户设置密码</p><p>&nbsp;id qian&nbsp; &nbsp; &nbsp;（表示查看是否有qian名字的用户）</p><p>&nbsp; 如果无：显示&nbsp; id: qian: no such user</p><p>&nbsp;&nbsp;</p><p>创建用户qian并且密码为qianhongshen</p><p>&nbsp; &nbsp;useradd qian</p><p>&nbsp; &nbsp;passwd qianhongshen</p><p><br></p><p>&nbsp;服务器端创建 Git 仓库</p><p>&nbsp; &nbsp;设置 /home/<USER>/git/gittest.git 为 Git 仓库</p><p>&nbsp; 然后把 Git 仓库的 owner 修改为用户名为qian的用户</p><p><br></p><p>&nbsp; mkdir -p /home/<USER>/git/gittest.git</p><p>&nbsp; 初始化一个git仓库</p><p>&nbsp; git init --bare data/git/gittest.git</p><p>会显示：Initialized empty Git repository in /home/<USER>/git/gittest.git/</p><p>&nbsp; &nbsp;cd /home/<USER>/git/</p><p>前一个git表示用户名，后一个git表示组名</p><p>&nbsp; &nbsp;chown -R git:git gittest.git/</p><p><br></p><p>服务器端 Git 打开 RSA 认证</p><p>&nbsp; 进入 /etc/ssh 目录，编辑 sshd_config，打开以下三个配置的注释：</p><p>RSAAuthentication yes</p><p>PubkeyAuthentication yes</p><p>AuthorizedKeysFile .ssh/authorized_keys</p><p>保存并重启 sshd 服务：（可能会找不到下面的文件 ）</p><p>&nbsp; /etc/rc.d/init.d/sshd restart</p><p>由 AuthorizedKeysFile 得知公钥的存放路径是 .ssh/authorized_keys，实际上是 $Home/.ssh/authorized_keys，由于管理 Git 服务的用户是 git，所以实际存放公钥的路径是 /home/<USER>/.ssh/authorized_keys</p><p>在 /home/<USER>/ 下创建目录 .ssh</p><p>&nbsp; cd /home/<USER>/p><p>&nbsp; &nbsp; mkdir .ssh</p><p>&nbsp; &nbsp;ls -a&nbsp;</p><p>然后把 .ssh 文件夹的 owner 修改为 git</p><p>chown -R git:git .ssh</p><p>ll -a</p><p>将客户端公钥导入服务器端 /home/<USER>/.ssh/authorized_keys 文件</p><p>回到 Git Bash 下，导入文件：</p><p>&nbsp;ssh git@192.168.56.101 \'cat &gt;&gt; .ssh/authorized_keys\' &lt; ~/.ssh/id_rsa.pub</p><p>需要输入服务器端 git 用户的密码</p><p><br></p><p>回到服务器端，查看 .ssh 下是否存在 authorized_keys 文件：</p><p>cd /home/<USER>/.ssh</p><p>重要：</p><p>修改 .ssh 目录的权限为 700</p><p>修改 .ssh/authorized_keys 文件的权限为 600</p><p>chmod 700 .ssh</p><p>cd .ssh</p><p>chmod 600 authorized_keys&nbsp;</p><p>完成！</p><p>使用 tortoiseGit 客户端来管理项目</p>', '2019-03-01 11:06:33', null, '', '30', null);
INSERT INTO `boke_article` VALUES ('50', 'Linux下安装redis', '<p>&nbsp;1,把文件传到linux服务器上 opt目录下</p><p>&nbsp; 解压：</p><p>&nbsp; &nbsp;tar -zxvf&nbsp; *****</p><p><br></p><p>2,进入解压后的目录中 安装gcc环境</p><p><br></p><p>&nbsp; yum&nbsp; install&nbsp; gcc&nbsp;</p><p><br></p><p>&nbsp; 验证gcc (可忽略)</p><p>&nbsp; &nbsp;rpm -qa |grep gcc&nbsp;</p><p>&nbsp;&nbsp;</p><p>&nbsp; &nbsp;执行编译：</p><p>&nbsp; &nbsp; make</p><p>&nbsp; &nbsp;（如果上面编译失败的话，执行下面语句）：</p><p>&nbsp; &nbsp; &nbsp;make MALLOC=libc</p><p><br></p><p>进入src目录就会有四个可执行文件redis-server、redis-benchmark、redis-cli和redis.conf（编译之前没有）</p><p>&nbsp;我安装的是3.2.12 redis.conf这个文件是在redis的目录下 不在redis的src目录下</p><p><br></p><p>3，安装redis</p><p>&nbsp; &nbsp; make install</p><p><br></p><p><br></p><p>4，为了方便管理，可以把redis常用的文件放一起（本示例是放在/usr/redis目录下的）</p><p><br></p><p>mkdir /usr/redis</p><p>cp redis-server&nbsp; /usr/redis</p><p>cp redis-benchmark /usr/redis</p><p>cp redis-cli&nbsp; /usr/redis</p><p><br></p><p>这里会返回上一次目录（以上都是在redis的src目录下cp的）</p><p>&nbsp;cd ../</p><p>cp redis.conf&nbsp; /usr/redis</p><p>cd /usr/redis</p><p><br></p><p>5，修改redis配置文件。</p><p>cd /usr/redis</p><p><br></p><p>&nbsp; &nbsp; &nbsp;vim redis.conf</p><p><br></p><p>&nbsp; &nbsp;（1）首先编辑conf文件，将daemonize属性改为yes（表明需要在后台运行）</p><p>&nbsp; &nbsp;&nbsp;</p><p>&nbsp; &nbsp; &nbsp;(2)把 #requirepass foobared 这一行取消注释 ，然后foobared 删除输入你的redis密码（redis设置密码，默认是没有密码的）</p><p><br></p><p>&nbsp; &nbsp; (3）把bind 127.0.0.1 给注释掉，这样就能接受所有来自于可用网络接口的连接（如果不注释，可能外面连接不上）</p><p>&nbsp; &nbsp; (4)&nbsp; 修改protected mode yes 保护模式，只允许本地连接（默认是yes，把yes改成no）</p><p><br></p><p>6，启动redis</p><p><br></p><p>cd /usr/redis</p><p>&nbsp;</p><p>./redis-server redis.conf</p><p><br></p><p>启动以后就可以用redis desktop Manager就行连接了（默认端口号是6379）</p><p>&nbsp; &nbsp;&nbsp;</p>', '2019-03-01 11:07:39', null, '安装redis', '30', null);
INSERT INTO `boke_article` VALUES ('51', 'mybatis多级查询', '<p><br></p><p>collection的用法</p><p><br></p><p>&lt;resultMap id=\"GoodsClassOneMap\" type=\"com.shanghe.dt.dao.object.GoodsClassDao\"</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;extends=\"com.shanghe.dt.dao.mapper.GoodsClassMapper.BaseResultMap\"&gt;</p><p>&nbsp; &lt;collection property=\"subClassList\" column=\"id\" select=\"queryGoodsClassTwo\"/&gt;</p><p>&lt;/resultMap&gt;</p><p>&lt;resultMap id=\"GoodsClassTwoMap\" type=\"com.shanghe.dt.dao.object.GoodsClassDao\"</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;extends=\"com.shanghe.dt.dao.mapper.GoodsClassMapper.BaseResultMap\"&gt;</p><p>&nbsp; &lt;collection property=\"subClassList\" column=\"id\" select=\"queryGoodsClassThree\"/&gt;</p><p>&lt;/resultMap&gt;</p><p>&lt;!--获取前三级所有分类--&gt;</p><p>&lt;select id=\"queryGoodsClassBeforeThree\" resultMap=\"GoodsClassOneMap\"&gt;</p><p>&nbsp; SELECT id,class_name,class_img</p><p>&nbsp; FROM dt_goods_class</p><p>&nbsp; WHERE parent_id = 0 AND is_delete = 0 AND is_show = 1</p><p>&nbsp; ORDER BY sort_value DESC;</p><p>&lt;/select&gt;</p><p>&lt;select id=\"queryGoodsClassTwo\" parameterType=\"java.lang.Long\" resultMap=\"GoodsClassTwoMap\"&gt;</p><p>&nbsp; SELECT id,class_name</p><p>&nbsp; FROM dt_goods_class</p><p>&nbsp; WHERE parent_id = #{id} AND `level` = 2 AND is_delete = 0 AND is_show = 1</p><p>&nbsp; ORDER BY sort_value DESC;</p><p>&lt;/select&gt;</p><p>&lt;select id=\"queryGoodsClassThree\" parameterType=\"java.lang.Long\" resultMap=\"com.shanghe.dt.dao.mapper.GoodsClassMapper.BaseResultMap\"&gt;</p><p>&nbsp; SELECT id,class_name</p><p>&nbsp; FROM dt_goods_class</p><p>&nbsp; WHERE parent_id = #{id} AND `level` = 3 AND is_delete = 0 AND is_show = 1</p><p>&nbsp; ORDER BY sort_value DESC;</p><p>&lt;/select&gt;</p><p><br></p><p>多个参数传递</p><p>&lt;collection property=\"subClassList\" column=\"{area_id=area_id,type=type}\" select=\"queryGoodsClassTwo\"/&gt;</p>', '2019-03-08 11:21:34', null, '利用collection进行多级循环调用', '31', null);
INSERT INTO `boke_article` VALUES ('52', 'ArrayList 的 subList使用', '<p>阿里巴巴有如下内容：</p><p>2. 【强制】 ArrayList 的 subList 结果不可强转成 ArrayList ，否则会抛出 ClassCastException</p><p>异常，即 java . util . RandomAccessSubList cannot be cast to java . util . ArrayList 。</p><p>说明： subList 返回的是 ArrayList 的内部类 SubList ，并不是 ArrayList 而是 ArrayList</p><p>的一个视图，对于 SubList 子列表的所有操作最终会反映到原列表上。</p><p><br></p><p>demo：</p><pre>ArrayList&lt;String&gt; list = <span>new </span>ArrayList&lt;&gt;()<span>;<br></span>list.add(<span>\"aaa\"</span>)<span>;<br></span>list.add(<span>\"bb\"</span>)<span>;<br></span>list.add(<span>\"vvv\"</span>)<span>;<br></span>List&lt;String&gt; list1 = list.subList(<span>0</span><span>, </span>list.size())<span>;<br></span>list1.remove(list.size()-<span>1</span>)<span>;<br></span>System.<span>out</span>.println(list)<span>;<br></span>System.<span>out</span>.println(list1)<span>;</span></pre><p><br></p><p>结果：</p><p>[aaa, bb]</p><p>[aaa, bb]</p><p><br></p><p>发现 list和list1的结果一致，且都是移除了指定下标的值。</p><p>由此验证了 对于 SubList 子列表的所有操作最终会反映到原列表上。</p><p><br></p><p><br></p><p><br></p>', '2019-06-28 16:14:16', null, '', '29', null);
INSERT INTO `boke_article` VALUES ('53', 'K8S', '<p><img src=\"http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1568277633652.jpg\" alt=\"file\"></p><p>哈哈哈哈哈</p>', '2019-09-12 16:41:07', null, '', '29', null);

-- ----------------------------
-- Table structure for boke_column
-- ----------------------------
DROP TABLE IF EXISTS `boke_column`;
CREATE TABLE `boke_column` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `name` varchar(15) DEFAULT NULL COMMENT '栏目名称',
  `link` varchar(100) DEFAULT NULL COMMENT '栏目链接',
  `description` varchar(100) DEFAULT NULL COMMENT '栏目描述',
  `parent_id` int(12) DEFAULT NULL COMMENT '父节点',
  `parent_name` varchar(20) DEFAULT NULL COMMENT '父节点名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of boke_column
-- ----------------------------
INSERT INTO `boke_column` VALUES ('28', 'java', '', '关于java的一些父分类', null, null);
INSERT INTO `boke_column` VALUES ('29', 'java后台', '', '关于java后台的内容', '28', 'java');
INSERT INTO `boke_column` VALUES ('30', '运维', '', '关于服务器的一些内容', '28', 'java');
INSERT INTO `boke_column` VALUES ('31', 'mybatis', '', '', '28', 'java');

-- ----------------------------
-- Table structure for boke_comments
-- ----------------------------
DROP TABLE IF EXISTS `boke_comments`;
CREATE TABLE `boke_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tourist_id` int(11) DEFAULT NULL COMMENT '游客id',
  `article_id` int(11) DEFAULT NULL COMMENT '文章id',
  `creat_time` datetime DEFAULT NULL COMMENT '创建时间',
  `leave_comments` varchar(50) DEFAULT NULL COMMENT '留言信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of boke_comments
-- ----------------------------
INSERT INTO `boke_comments` VALUES ('14', '8', '41', '2019-05-08 15:25:44', '666');
INSERT INTO `boke_comments` VALUES ('15', '8', '41', '2019-05-08 16:02:59', '');

-- ----------------------------
-- Table structure for boke_tourist
-- ----------------------------
DROP TABLE IF EXISTS `boke_tourist`;
CREATE TABLE `boke_tourist` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `nickname` varchar(15) DEFAULT NULL COMMENT '昵称',
  `mailbox` varchar(15) DEFAULT NULL COMMENT '邮箱',
  `username` varchar(15) DEFAULT NULL COMMENT '账号',
  `password` varchar(15) DEFAULT NULL COMMENT '密码',
  `head_portrait` varchar(100) DEFAULT NULL COMMENT '头像',
  `creat_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of boke_tourist
-- ----------------------------
INSERT INTO `boke_tourist` VALUES ('8', '一只虾', null, 'admin', '123456', null, '2019-05-08 15:25:26');
INSERT INTO `boke_tourist` VALUES ('9', '清扬', null, 'qingyang', 'QINGYANG', null, '2019-07-05 12:58:02');
INSERT INTO `boke_tourist` VALUES ('10', '小虫夏', null, 'admin', 'admin123', null, '2019-09-26 09:50:28');

-- ----------------------------
-- Table structure for boke_user
-- ----------------------------
DROP TABLE IF EXISTS `boke_user`;
CREATE TABLE `boke_user` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `username` varchar(15) NOT NULL COMMENT '登录账号',
  `password` varchar(15) NOT NULL COMMENT '登录密码',
  `name` varchar(15) NOT NULL COMMENT '用户名',
  `head_portrait` varchar(100) DEFAULT NULL COMMENT '用户头像',
  `wechat` varchar(100) DEFAULT NULL COMMENT '用户微信',
  `qq_number` varchar(100) DEFAULT NULL COMMENT '用户QQ',
  `weibo` varchar(100) DEFAULT NULL COMMENT '用户微博',
  `signature` varchar(50) DEFAULT NULL COMMENT '用户签名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of boke_user
-- ----------------------------
INSERT INTO `boke_user` VALUES ('14', 'admin', '123456', 'qhs', 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1557281737620.jpg', 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1557282019313.jpg', 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1557282051241.jpg', '', '欢迎回家！');

-- ----------------------------
-- Table structure for jianli_appraisal
-- ----------------------------
DROP TABLE IF EXISTS `jianli_appraisal`;
CREATE TABLE `jianli_appraisal` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `content` varchar(255) DEFAULT NULL COMMENT '自我评价内容',
  `gmt_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='简历模块-自我评价';

-- ----------------------------
-- Records of jianli_appraisal
-- ----------------------------
INSERT INTO `jianli_appraisal` VALUES ('4', '具有较好的团队合作意识，有良好的沟通能力', '2019-10-07 16:23:07', '2019-10-07 16:23:07');
INSERT INTO `jianli_appraisal` VALUES ('5', '认真仔细，富有耐心，解决问题能力强', '2019-10-07 16:23:15', '2019-10-07 16:23:15');
INSERT INTO `jianli_appraisal` VALUES ('6', '有较强的学习能力', '2019-10-07 16:23:23', '2019-10-07 16:23:23');

-- ----------------------------
-- Table structure for jianli_project
-- ----------------------------
DROP TABLE IF EXISTS `jianli_project`;
CREATE TABLE `jianli_project` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `project_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
  `project_skill` varchar(255) DEFAULT NULL COMMENT '项目使用技术',
  `project_time` varchar(20) DEFAULT NULL COMMENT '项目周期',
  `project_details` text COMMENT '项目详情',
  `project_class` varchar(2) DEFAULT NULL COMMENT '项目类别',
  `project_img` varchar(255) DEFAULT NULL COMMENT '项目图片',
  `gmt_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 COMMENT='简历模块-项目介绍';

-- ----------------------------
-- Records of jianli_project
-- ----------------------------
INSERT INTO `jianli_project` VALUES ('1', '城市通', 'Springboot+Spring+Mybatis+Shiro+Layui+Thymeleaf+JavaScript+jQuery+ajax+swagger', '2019.06-至今', '', null, 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1570439391229.jpg', '2019-10-07 16:14:51', '2019-11-20 17:56:44');
INSERT INTO `jianli_project` VALUES ('3', '际智慧城市', 'SpringBoot+Spring+Mybatis+Shiro+Layui+Thymeleaf+webpack+node.js+Swagger+JsonWebToken+JavaScript+jQuery+ajax', '2018.11-至今', '', null, 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1570435931833.jpg', '2019-10-07 16:12:19', '2019-11-20 17:57:22');

-- ----------------------------
-- Table structure for jianli_skill
-- ----------------------------
DROP TABLE IF EXISTS `jianli_skill`;
CREATE TABLE `jianli_skill` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `skill_details` varchar(255) DEFAULT NULL COMMENT '技能描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='简历模块-个人技能';

-- ----------------------------
-- Records of jianli_skill
-- ----------------------------
INSERT INTO `jianli_skill` VALUES ('7', '熟练掌握javaEE基础编程以及熟练使用resultful编程,对java中常见的设计模式以及面向对象的设计思想有一定的实践,并具有良好的编程习惯');
INSERT INTO `jianli_skill` VALUES ('8', '熟悉使用javaEE中常见开源框架Spring、Springmvc、mybatis、hibernate等');
INSERT INTO `jianli_skill` VALUES ('10', '熟练掌握javaEE中常见开源组件技术 Quartz 、spring 缓存、poi等');
INSERT INTO `jianli_skill` VALUES ('11', '熟悉使用HTML,JavaScript,jQuery,Ajax,thymeleaf,jsp和layui模板等Web前端技术');
INSERT INTO `jianli_skill` VALUES ('12', '熟练使用MySQL数据库，有多个使用mysql数据库的项目经验，能编写一些业务逻辑较复杂的sql');
INSERT INTO `jianli_skill` VALUES ('13', '熟练掌握javaEE中常见开发工具Intellij IDEA、Eclipse,以及项目构建工具maven,以及版本控制工具SVN、GIT使用');
INSERT INTO `jianli_skill` VALUES ('16', '了解Redis作为缓存框架，并使用jedis进行项目的开发');
INSERT INTO `jianli_skill` VALUES ('18', '了解消息中间件activeMQ、kafka等的使用');

-- ----------------------------
-- Table structure for jianli_statistics
-- ----------------------------
DROP TABLE IF EXISTS `jianli_statistics`;
CREATE TABLE `jianli_statistics` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `ip` varchar(50) DEFAULT NULL COMMENT '访问ip',
  `interface_name` varchar(50) DEFAULT NULL COMMENT '访问接口名',
  `count` int(10) DEFAULT NULL COMMENT '访问次数',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `gmt_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=339 DEFAULT CHARSET=utf8 COMMENT='接口访问统计表';

-- ----------------------------
-- Records of jianli_statistics
-- ----------------------------
INSERT INTO `jianli_statistics` VALUES ('246', '**************', '/', '2', '2019-10-07 18:03:58', '2019-10-07 17:55:56', '2019-10-07 18:03:58');
INSERT INTO `jianli_statistics` VALUES ('247', '*************', '/', '1', '2019-10-07 19:13:25', '2019-10-07 19:13:25', '2019-10-07 19:13:25');
INSERT INTO `jianli_statistics` VALUES ('248', '************', '/', '9', '2019-10-08 20:06:41', '2019-10-08 09:54:28', '2019-10-08 20:06:40');
INSERT INTO `jianli_statistics` VALUES ('250', '113.250.231.191', '/', '2', '2019-10-09 07:47:04', '2019-10-08 13:15:51', '2019-10-09 07:47:03');
INSERT INTO `jianli_statistics` VALUES ('251', '113.250.231.191', '/jianli/sendEmail', '1', '2019-10-08 13:19:12', '2019-10-08 13:19:12', '2019-10-08 13:19:12');
INSERT INTO `jianli_statistics` VALUES ('252', '58.247.206.158', '/', '2', '2019-10-24 19:04:30', '2019-10-08 17:52:16', '2019-10-24 19:04:30');
INSERT INTO `jianli_statistics` VALUES ('253', '61.151.178.174', '/', '1', '2019-10-08 17:52:25', '2019-10-08 17:52:25', '2019-10-08 17:52:25');
INSERT INTO `jianli_statistics` VALUES ('254', '47.101.200.118', '/', '1', '2019-10-09 09:04:51', '2019-10-09 09:04:51', '2019-10-09 09:04:51');
INSERT INTO `jianli_statistics` VALUES ('255', '14.106.229.236', '/', '1', '2019-10-09 09:41:06', '2019-10-09 09:41:06', '2019-10-09 09:41:06');
INSERT INTO `jianli_statistics` VALUES ('256', '14.106.229.236', '/jianli/download', '1', '2019-10-09 09:41:29', '2019-10-09 09:41:29', '2019-10-09 09:41:29');
INSERT INTO `jianli_statistics` VALUES ('257', '42.236.10.114', '/', '1', '2019-10-09 09:41:32', '2019-10-09 09:41:32', '2019-10-09 09:41:32');
INSERT INTO `jianli_statistics` VALUES ('258', '180.163.220.3', '/jianli/download', '1', '2019-10-09 09:41:51', '2019-10-09 09:41:51', '2019-10-09 09:41:51');
INSERT INTO `jianli_statistics` VALUES ('259', '180.163.220.5', '/jianli/download', '1', '2019-10-09 09:41:59', '2019-10-09 09:41:59', '2019-10-09 09:41:59');
INSERT INTO `jianli_statistics` VALUES ('260', '61.129.6.159', '/', '1', '2019-10-09 09:42:04', '2019-10-09 09:42:04', '2019-10-09 09:42:04');
INSERT INTO `jianli_statistics` VALUES ('261', '47.101.196.6', '/', '1', '2019-10-10 08:25:09', '2019-10-10 08:25:09', '2019-10-10 08:25:09');
INSERT INTO `jianli_statistics` VALUES ('262', '101.4.60.63', '/', '1', '2019-10-10 11:04:28', '2019-10-10 11:04:28', '2019-10-10 11:04:28');
INSERT INTO `jianli_statistics` VALUES ('263', '125.82.184.171', '/', '1', '2019-10-10 11:11:47', '2019-10-10 11:11:47', '2019-10-10 11:11:47');
INSERT INTO `jianli_statistics` VALUES ('264', '101.89.239.230', '/', '1', '2019-10-10 11:12:47', '2019-10-10 11:12:47', '2019-10-10 11:12:47');
INSERT INTO `jianli_statistics` VALUES ('265', '101.91.60.105', '/', '1', '2019-10-10 11:12:51', '2019-10-10 11:12:51', '2019-10-10 11:12:51');
INSERT INTO `jianli_statistics` VALUES ('266', '14.104.205.164', '/', '1', '2019-10-10 13:46:54', '2019-10-10 13:46:54', '2019-10-10 13:46:54');
INSERT INTO `jianli_statistics` VALUES ('267', '211.150.82.150', '/boke.html', '1', '2019-10-10 13:55:34', '2019-10-10 13:55:34', '2019-10-10 13:55:34');
INSERT INTO `jianli_statistics` VALUES ('268', '125.82.191.66', '/boke.html', '1', '2019-10-10 18:19:48', '2019-10-10 18:19:48', '2019-10-10 18:19:48');
INSERT INTO `jianli_statistics` VALUES ('269', '125.82.191.66', '/', '1', '2019-10-10 18:20:32', '2019-10-10 18:20:32', '2019-10-10 18:20:32');
INSERT INTO `jianli_statistics` VALUES ('270', '101.227.139.171', '/boke.html', '1', '2019-10-10 18:20:48', '2019-10-10 18:20:48', '2019-10-10 18:20:48');
INSERT INTO `jianli_statistics` VALUES ('271', '101.89.19.149', '/boke.html', '1', '2019-10-10 18:20:50', '2019-10-10 18:20:50', '2019-10-10 18:20:50');
INSERT INTO `jianli_statistics` VALUES ('272', '47.101.56.210', '/', '1', '2019-10-10 19:55:55', '2019-10-10 19:55:55', '2019-10-10 19:55:55');
INSERT INTO `jianli_statistics` VALUES ('273', '101.132.194.119', '/', '1', '2019-10-11 07:39:48', '2019-10-11 07:39:48', '2019-10-11 07:39:48');
INSERT INTO `jianli_statistics` VALUES ('274', '36.99.136.132', '/', '2', '2019-10-11 22:50:20', '2019-10-11 22:50:16', '2019-10-11 22:50:19');
INSERT INTO `jianli_statistics` VALUES ('275', '111.7.100.23', '/boke.html', '1', '2019-10-11 22:50:25', '2019-10-11 22:50:25', '2019-10-11 22:50:25');
INSERT INTO `jianli_statistics` VALUES ('276', '58.60.15.41', '/', '1', '2019-10-11 22:50:26', '2019-10-11 22:50:26', '2019-10-11 22:50:26');
INSERT INTO `jianli_statistics` VALUES ('277', '59.36.129.20', '/', '1', '2019-10-11 22:50:27', '2019-10-11 22:50:27', '2019-10-11 22:50:27');
INSERT INTO `jianli_statistics` VALUES ('278', '59.36.129.45', '/', '1', '2019-10-11 22:50:28', '2019-10-11 22:50:28', '2019-10-11 22:50:28');
INSERT INTO `jianli_statistics` VALUES ('279', '111.7.100.22', '/boke.html', '1', '2019-10-11 22:50:31', '2019-10-11 22:50:31', '2019-10-11 22:50:31');
INSERT INTO `jianli_statistics` VALUES ('280', '180.149.139.180', '/', '1', '2019-10-11 22:50:32', '2019-10-11 22:50:32', '2019-10-11 22:50:32');
INSERT INTO `jianli_statistics` VALUES ('281', '123.125.106.81', '/', '1', '2019-10-11 22:50:36', '2019-10-11 22:50:36', '2019-10-11 22:50:36');
INSERT INTO `jianli_statistics` VALUES ('282', '47.101.200.206', '/', '1', '2019-10-12 08:25:22', '2019-10-12 08:25:22', '2019-10-12 08:25:22');
INSERT INTO `jianli_statistics` VALUES ('283', '47.101.199.215', '/', '1', '2019-10-13 08:10:50', '2019-10-13 08:10:50', '2019-10-13 08:10:50');
INSERT INTO `jianli_statistics` VALUES ('284', '47.101.187.41', '/', '1', '2019-10-14 08:25:36', '2019-10-14 08:25:36', '2019-10-14 08:25:36');
INSERT INTO `jianli_statistics` VALUES ('285', '47.100.234.45', '/', '1', '2019-10-15 05:24:57', '2019-10-15 05:24:57', '2019-10-15 05:24:57');
INSERT INTO `jianli_statistics` VALUES ('286', '127.0.0.1', '/boke.html', '4', '2019-11-21 16:23:24', '2019-10-15 15:59:25', '2019-11-21 16:23:23');
INSERT INTO `jianli_statistics` VALUES ('287', '106.87.72.94', '/', '11', '2019-10-16 10:05:39', '2019-10-16 09:59:58', '2019-10-16 10:05:39');
INSERT INTO `jianli_statistics` VALUES ('288', '47.101.50.11', '/', '1', '2019-10-16 12:04:16', '2019-10-16 12:04:16', '2019-10-16 12:04:16');
INSERT INTO `jianli_statistics` VALUES ('289', '47.101.196.225', '/', '1', '2019-10-18 09:55:15', '2019-10-18 09:55:15', '2019-10-18 09:55:15');
INSERT INTO `jianli_statistics` VALUES ('290', '47.101.195.248', '/', '1', '2019-10-19 07:50:25', '2019-10-19 07:50:25', '2019-10-19 07:50:25');
INSERT INTO `jianli_statistics` VALUES ('291', '47.101.195.38', '/', '1', '2019-10-20 10:22:59', '2019-10-20 10:22:59', '2019-10-20 10:22:59');
INSERT INTO `jianli_statistics` VALUES ('292', '183.230.155.13', '/boke.html', '1', '2019-10-21 16:48:36', '2019-10-21 16:48:36', '2019-10-21 16:48:36');
INSERT INTO `jianli_statistics` VALUES ('293', '183.230.155.13', '/article/detail.html', '2', '2019-10-21 16:49:10', '2019-10-21 16:48:51', '2019-10-21 16:49:10');
INSERT INTO `jianli_statistics` VALUES ('294', '183.230.155.13', '/article/list.html', '1', '2019-10-21 16:49:04', '2019-10-21 16:49:04', '2019-10-21 16:49:04');
INSERT INTO `jianli_statistics` VALUES ('295', '47.101.202.13', '/', '1', '2019-10-23 08:16:09', '2019-10-23 08:16:09', '2019-10-23 08:16:09');
INSERT INTO `jianli_statistics` VALUES ('296', '47.101.192.16', '/', '1', '2019-10-24 08:57:25', '2019-10-24 08:57:25', '2019-10-24 08:57:25');
INSERT INTO `jianli_statistics` VALUES ('297', '123.147.246.76, 119.84.153.195', '/', '1', '2019-10-24 19:03:31', '2019-10-24 19:03:31', '2019-10-24 19:03:31');
INSERT INTO `jianli_statistics` VALUES ('298', '113.7.164.175', '/', '2', '2019-10-24 19:15:46', '2019-10-24 19:04:06', '2019-10-24 19:15:45');
INSERT INTO `jianli_statistics` VALUES ('299', '123.147.246.76, 58.144.156.114', '/', '1', '2019-10-24 19:04:29', '2019-10-24 19:04:29', '2019-10-24 19:04:29');
INSERT INTO `jianli_statistics` VALUES ('300', '61.151.178.175', '/', '1', '2019-10-24 19:04:47', '2019-10-24 19:04:47', '2019-10-24 19:04:47');
INSERT INTO `jianli_statistics` VALUES ('301', '113.7.164.175', '/jianli/download', '1', '2019-10-24 19:05:20', '2019-10-24 19:05:20', '2019-10-24 19:05:20');
INSERT INTO `jianli_statistics` VALUES ('302', '125.82.184.225', '/', '2', '2019-10-24 20:04:17', '2019-10-24 20:03:55', '2019-10-24 20:04:16');
INSERT INTO `jianli_statistics` VALUES ('303', '125.82.184.225', '/jianli/download', '1', '2019-10-24 20:04:19', '2019-10-24 20:04:19', '2019-10-24 20:04:19');
INSERT INTO `jianli_statistics` VALUES ('304', '47.100.7.135', '/', '1', '2019-10-24 21:02:38', '2019-10-24 21:02:38', '2019-10-24 21:02:38');
INSERT INTO `jianli_statistics` VALUES ('305', '47.103.21.0', '/', '1', '2019-10-25 05:25:02', '2019-10-25 05:25:02', '2019-10-25 05:25:02');
INSERT INTO `jianli_statistics` VALUES ('306', '125.82.184.225, 119.84.153.195', '/', '1', '2019-10-26 18:09:17', '2019-10-26 18:09:17', '2019-10-26 18:09:17');
INSERT INTO `jianli_statistics` VALUES ('307', '180.97.118.223', '/', '1', '2019-10-26 18:10:16', '2019-10-26 18:10:16', '2019-10-26 18:10:16');
INSERT INTO `jianli_statistics` VALUES ('308', '101.89.239.216', '/', '1', '2019-10-26 18:10:19', '2019-10-26 18:10:19', '2019-10-26 18:10:19');
INSERT INTO `jianli_statistics` VALUES ('309', '47.101.202.135', '/', '1', '2019-10-27 08:23:38', '2019-10-27 08:23:38', '2019-10-27 08:23:38');
INSERT INTO `jianli_statistics` VALUES ('310', '116.62.112.111', '/', '1', '2019-10-28 06:13:38', '2019-10-28 06:13:38', '2019-10-28 06:13:38');
INSERT INTO `jianli_statistics` VALUES ('311', '123.147.246.76', '/', '2', '2019-10-28 10:26:29', '2019-10-28 10:25:51', '2019-10-28 10:26:28');
INSERT INTO `jianli_statistics` VALUES ('312', '123.147.246.76', '/boke.html', '2', '2019-10-28 10:27:27', '2019-10-28 10:26:05', '2019-10-28 10:27:27');
INSERT INTO `jianli_statistics` VALUES ('313', '47.100.108.86', '/', '1', '2019-10-28 12:53:12', '2019-10-28 12:53:12', '2019-10-28 12:53:12');
INSERT INTO `jianli_statistics` VALUES ('314', '111.206.36.145', '/boke.html', '1', '2019-10-28 13:19:46', '2019-10-28 13:19:46', '2019-10-28 13:19:46');
INSERT INTO `jianli_statistics` VALUES ('315', '111.206.36.145', '/', '1', '2019-10-28 15:50:51', '2019-10-28 15:50:51', '2019-10-28 15:50:51');
INSERT INTO `jianli_statistics` VALUES ('316', '14.215.176.149', '/', '1', '2019-10-28 20:58:29', '2019-10-28 20:58:29', '2019-10-28 20:58:29');
INSERT INTO `jianli_statistics` VALUES ('317', '47.101.192.18', '/', '1', '2019-10-29 04:38:27', '2019-10-29 04:38:27', '2019-10-29 04:38:27');
INSERT INTO `jianli_statistics` VALUES ('318', '47.101.191.143', '/', '1', '2019-10-30 08:17:51', '2019-10-30 08:17:51', '2019-10-30 08:17:51');
INSERT INTO `jianli_statistics` VALUES ('319', '47.101.187.185', '/', '1', '2019-10-31 07:56:19', '2019-10-31 07:56:19', '2019-10-31 07:56:19');
INSERT INTO `jianli_statistics` VALUES ('320', '47.100.166.87', '/', '1', '2019-11-01 07:12:21', '2019-11-01 07:12:21', '2019-11-01 07:12:21');
INSERT INTO `jianli_statistics` VALUES ('321', '127.0.0.1', '/', '2', '2019-11-21 16:23:09', '2019-11-04 09:27:17', '2019-11-21 16:23:08');
INSERT INTO `jianli_statistics` VALUES ('322', '110.228.245.190', '/', '1', '2019-11-07 19:34:38', '2019-11-07 19:34:38', '2019-11-07 19:34:38');
INSERT INTO `jianli_statistics` VALUES ('323', '101.132.169.125', '/', '1', '2019-11-07 21:09:43', '2019-11-07 21:09:43', '2019-11-07 21:09:43');
INSERT INTO `jianli_statistics` VALUES ('324', '14.111.63.254', '/boke.html', '6', '2019-11-21 15:36:03', '2019-11-20 17:47:55', '2019-11-21 15:36:03');
INSERT INTO `jianli_statistics` VALUES ('325', '14.111.63.254', '/', '9', '2019-11-21 15:35:49', '2019-11-20 17:48:25', '2019-11-21 15:35:49');
INSERT INTO `jianli_statistics` VALUES ('326', '101.89.29.92', '/', '1', '2019-11-20 17:49:26', '2019-11-20 17:49:26', '2019-11-20 17:49:26');
INSERT INTO `jianli_statistics` VALUES ('327', '14.111.63.254', '/jianli/download', '1', '2019-11-20 17:50:43', '2019-11-20 17:50:43', '2019-11-20 17:50:43');
INSERT INTO `jianli_statistics` VALUES ('328', '61.151.178.166', '/jianli/download', '1', '2019-11-20 17:51:44', '2019-11-20 17:51:44', '2019-11-20 17:51:44');
INSERT INTO `jianli_statistics` VALUES ('329', '122.51.66.154', '/', '2', '2019-11-20 20:55:38', '2019-11-20 17:52:52', '2019-11-20 20:55:38');
INSERT INTO `jianli_statistics` VALUES ('330', '14.111.63.254', '/article/detail.html', '2', '2019-11-20 18:00:06', '2019-11-20 17:58:22', '2019-11-20 18:00:06');
INSERT INTO `jianli_statistics` VALUES ('331', '14.111.63.254', '/article/list.html', '1', '2019-11-20 18:00:19', '2019-11-20 18:00:19', '2019-11-20 18:00:19');
INSERT INTO `jianli_statistics` VALUES ('332', '14.111.63.254', '/jianli/sendEmail', '1', '2019-11-20 18:10:49', '2019-11-20 18:10:49', '2019-11-20 18:10:49');
INSERT INTO `jianli_statistics` VALUES ('333', '122.51.237.173', '/', '1', '2019-11-20 18:25:28', '2019-11-20 18:25:28', '2019-11-20 18:25:28');
INSERT INTO `jianli_statistics` VALUES ('334', '122.51.188.110', '/', '2', '2019-11-21 05:14:07', '2019-11-20 20:01:38', '2019-11-21 05:14:07');
INSERT INTO `jianli_statistics` VALUES ('335', '122.51.19.63', '/', '1', '2019-11-20 22:38:51', '2019-11-20 22:38:51', '2019-11-20 22:38:51');
INSERT INTO `jianli_statistics` VALUES ('336', '122.51.158.77', '/', '6', '2019-11-21 09:53:52', '2019-11-21 00:29:20', '2019-11-21 09:53:52');
INSERT INTO `jianli_statistics` VALUES ('337', '122.51.184.60', '/', '1', '2019-11-21 14:09:56', '2019-11-21 14:09:56', '2019-11-21 14:09:56');
INSERT INTO `jianli_statistics` VALUES ('338', '************', '/', '1', '2019-11-21 15:36:49', '2019-11-21 15:36:49', '2019-11-21 15:36:49');

-- ----------------------------
-- Table structure for jianli_user_info
-- ----------------------------
DROP TABLE IF EXISTS `jianli_user_info`;
CREATE TABLE `jianli_user_info` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(10) DEFAULT NULL COMMENT '姓名',
  `sex` varchar(1) DEFAULT NULL COMMENT '性别',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `address` varchar(100) DEFAULT NULL COMMENT '户口地址',
  `link_address` varchar(100) DEFAULT NULL COMMENT '联系地址',
  `specialty` varchar(50) DEFAULT NULL COMMENT '专业',
  `education` varchar(10) DEFAULT NULL COMMENT '学历',
  `school` varchar(50) DEFAULT NULL COMMENT '学校',
  `link_qq` varchar(20) DEFAULT NULL COMMENT 'qq',
  `link_wechat` varchar(20) DEFAULT NULL COMMENT '微信',
  `link_phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `link_email` varchar(20) DEFAULT NULL COMMENT '联系邮箱',
  `job` varchar(20) DEFAULT NULL COMMENT '应聘岗位',
  `gmt_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='简历模块-个人基本信息';

-- ----------------------------
-- Records of jianli_user_info
-- ----------------------------
INSERT INTO `jianli_user_info` VALUES ('2', '钱多多', '男', '25', '重庆', '重庆渝北', '-', '-', '-', '-', '-', '-', '<EMAIL>', 'JAVA工程师', '2019-10-07 16:01:25', '2019-11-20 17:53:00');

-- ----------------------------
-- Table structure for jianli_work
-- ----------------------------
DROP TABLE IF EXISTS `jianli_work`;
CREATE TABLE `jianli_work` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(50) DEFAULT NULL COMMENT '公司名称',
  `job` varchar(20) DEFAULT NULL COMMENT '任职职位',
  `work_content` varchar(255) DEFAULT NULL COMMENT '任职工作内容',
  `start_time` date DEFAULT NULL COMMENT '入职时间',
  `end_time` date DEFAULT NULL COMMENT '离职时间',
  `company_img` varchar(255) DEFAULT NULL COMMENT '公司图片',
  `gmt_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='简历模块-工作经历';

-- ----------------------------
-- Records of jianli_work
-- ----------------------------
INSERT INTO `jianli_work` VALUES ('5', '-', 'java软件工程师', '', '2040-08-15', '2040-10-01', 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1570436397552.jpg', '2019-10-07 16:20:15', '2019-11-20 17:54:54');
INSERT INTO `jianli_work` VALUES ('6', '-', 'java软件工程师', '', '2030-05-29', '2030-06-01', 'http://hongshenboke.oss-cn-hangzhou.aliyuncs.com/data/1570436540761.jpg', '2019-10-07 16:22:43', '2019-11-20 17:55:17');

-- ----------------------------
-- Table structure for tb_file
-- ----------------------------
DROP TABLE IF EXISTS `tb_file`;
CREATE TABLE `tb_file` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `fcontent` text NOT NULL,
  `fname` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of tb_file
-- ----------------------------
INSERT INTO `tb_file` VALUES ('2', 'ww', 'ww');
