<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.jianli.mapper.ProjectMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.jianli.object.ProjectDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_skill" jdbcType="VARCHAR" property="projectSkill" />
    <result column="project_time" jdbcType="VARCHAR" property="projectTime" />
    <result column="project_class" jdbcType="VARCHAR" property="projectClass" />
    <result column="project_img" jdbcType="VARCHAR" property="projectImg" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.hongshen.boke.dao.jianli.object.ProjectDO">
    <result column="project_details" jdbcType="LONGVARCHAR" property="projectDetails" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_name, project_skill, project_time, project_class, project_img
  </sql>
  <sql id="Blob_Column_List">
    project_details
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from jianli_project
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from jianli_project
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample">
    delete from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.jianli.object.ProjectDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_project (project_name, project_skill, project_time, 
      project_class, project_img, project_details
      )
    values (#{projectName,jdbcType=VARCHAR}, #{projectSkill,jdbcType=VARCHAR}, #{projectTime,jdbcType=VARCHAR}, 
      #{projectClass,jdbcType=VARCHAR}, #{projectImg,jdbcType=VARCHAR}, #{projectDetails,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.jianli.object.ProjectDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectSkill != null">
        project_skill,
      </if>
      <if test="projectTime != null">
        project_time,
      </if>
      <if test="projectClass != null">
        project_class,
      </if>
      <if test="projectImg != null">
        project_img,
      </if>
      <if test="projectDetails != null">
        project_details,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSkill != null">
        #{projectSkill,jdbcType=VARCHAR},
      </if>
      <if test="projectTime != null">
        #{projectTime,jdbcType=VARCHAR},
      </if>
      <if test="projectClass != null">
        #{projectClass,jdbcType=VARCHAR},
      </if>
      <if test="projectImg != null">
        #{projectImg,jdbcType=VARCHAR},
      </if>
      <if test="projectDetails != null">
        #{projectDetails,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample" resultType="java.lang.Long">
    select count(*) from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jianli_project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.projectSkill != null">
        project_skill = #{record.projectSkill,jdbcType=VARCHAR},
      </if>
      <if test="record.projectTime != null">
        project_time = #{record.projectTime,jdbcType=VARCHAR},
      </if>
      <if test="record.projectClass != null">
        project_class = #{record.projectClass,jdbcType=VARCHAR},
      </if>
      <if test="record.projectImg != null">
        project_img = #{record.projectImg,jdbcType=VARCHAR},
      </if>
      <if test="record.projectDetails != null">
        project_details = #{record.projectDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update jianli_project
    set id = #{record.id,jdbcType=INTEGER},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      project_skill = #{record.projectSkill,jdbcType=VARCHAR},
      project_time = #{record.projectTime,jdbcType=VARCHAR},
      project_class = #{record.projectClass,jdbcType=VARCHAR},
      project_img = #{record.projectImg,jdbcType=VARCHAR},
      project_details = #{record.projectDetails,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jianli_project
    set id = #{record.id,jdbcType=INTEGER},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      project_skill = #{record.projectSkill,jdbcType=VARCHAR},
      project_time = #{record.projectTime,jdbcType=VARCHAR},
      project_class = #{record.projectClass,jdbcType=VARCHAR},
      project_img = #{record.projectImg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.jianli.object.ProjectDO">
    update jianli_project
    <set>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectSkill != null">
        project_skill = #{projectSkill,jdbcType=VARCHAR},
      </if>
      <if test="projectTime != null">
        project_time = #{projectTime,jdbcType=VARCHAR},
      </if>
      <if test="projectClass != null">
        project_class = #{projectClass,jdbcType=VARCHAR},
      </if>
      <if test="projectImg != null">
        project_img = #{projectImg,jdbcType=VARCHAR},
      </if>
      <if test="projectDetails != null">
        project_details = #{projectDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.hongshen.boke.dao.jianli.object.ProjectDO">
    update jianli_project
    set project_name = #{projectName,jdbcType=VARCHAR},
      project_skill = #{projectSkill,jdbcType=VARCHAR},
      project_time = #{projectTime,jdbcType=VARCHAR},
      project_class = #{projectClass,jdbcType=VARCHAR},
      project_img = #{projectImg,jdbcType=VARCHAR},
      project_details = #{projectDetails,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.jianli.object.ProjectDO">
    update jianli_project
    set project_name = #{projectName,jdbcType=VARCHAR},
      project_skill = #{projectSkill,jdbcType=VARCHAR},
      project_time = #{projectTime,jdbcType=VARCHAR},
      project_class = #{projectClass,jdbcType=VARCHAR},
      project_img = #{projectImg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.jianli.object.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>