<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.jianli.mapper.UserinfoMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.jianli.object.UserinfoDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="link_address" jdbcType="VARCHAR" property="linkAddress" />
    <result column="specialty" jdbcType="VARCHAR" property="specialty" />
    <result column="education" jdbcType="VARCHAR" property="education" />
    <result column="school" jdbcType="VARCHAR" property="school" />
    <result column="link_qq" jdbcType="VARCHAR" property="linkQq" />
    <result column="link_wechat" jdbcType="VARCHAR" property="linkWechat" />
    <result column="link_phone" jdbcType="VARCHAR" property="linkPhone" />
    <result column="link_email" jdbcType="VARCHAR" property="linkEmail" />
    <result column="job" jdbcType="VARCHAR" property="job" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, sex, age, address, link_address, specialty, education, school, link_qq, 
    link_wechat, link_phone, link_email, job
  </sql>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jianli_user_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from jianli_user_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoExample">
    delete from jianli_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_user_info (name, sex, age, 
      address, link_address, specialty, 
      education, school, link_qq, 
      link_wechat, link_phone, link_email, 
      job)
    values (#{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER}, 
      #{address,jdbcType=VARCHAR}, #{linkAddress,jdbcType=VARCHAR}, #{specialty,jdbcType=VARCHAR}, 
      #{education,jdbcType=VARCHAR}, #{school,jdbcType=VARCHAR}, #{linkQq,jdbcType=VARCHAR}, 
      #{linkWechat,jdbcType=VARCHAR}, #{linkPhone,jdbcType=VARCHAR}, #{linkEmail,jdbcType=VARCHAR}, 
      #{job,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jianli_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="linkAddress != null">
        link_address,
      </if>
      <if test="specialty != null">
        specialty,
      </if>
      <if test="education != null">
        education,
      </if>
      <if test="school != null">
        school,
      </if>
      <if test="linkQq != null">
        link_qq,
      </if>
      <if test="linkWechat != null">
        link_wechat,
      </if>
      <if test="linkPhone != null">
        link_phone,
      </if>
      <if test="linkEmail != null">
        link_email,
      </if>
      <if test="job != null">
        job,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkAddress != null">
        #{linkAddress,jdbcType=VARCHAR},
      </if>
      <if test="specialty != null">
        #{specialty,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        #{education,jdbcType=VARCHAR},
      </if>
      <if test="school != null">
        #{school,jdbcType=VARCHAR},
      </if>
      <if test="linkQq != null">
        #{linkQq,jdbcType=VARCHAR},
      </if>
      <if test="linkWechat != null">
        #{linkWechat,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null">
        #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null">
        #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        #{job,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoExample" resultType="java.lang.Long">
    select count(*) from jianli_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jianli_user_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        age = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.linkAddress != null">
        link_address = #{record.linkAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.specialty != null">
        specialty = #{record.specialty,jdbcType=VARCHAR},
      </if>
      <if test="record.education != null">
        education = #{record.education,jdbcType=VARCHAR},
      </if>
      <if test="record.school != null">
        school = #{record.school,jdbcType=VARCHAR},
      </if>
      <if test="record.linkQq != null">
        link_qq = #{record.linkQq,jdbcType=VARCHAR},
      </if>
      <if test="record.linkWechat != null">
        link_wechat = #{record.linkWechat,jdbcType=VARCHAR},
      </if>
      <if test="record.linkPhone != null">
        link_phone = #{record.linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.linkEmail != null">
        link_email = #{record.linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.job != null">
        job = #{record.job,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jianli_user_info
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      sex = #{record.sex,jdbcType=VARCHAR},
      age = #{record.age,jdbcType=INTEGER},
      address = #{record.address,jdbcType=VARCHAR},
      link_address = #{record.linkAddress,jdbcType=VARCHAR},
      specialty = #{record.specialty,jdbcType=VARCHAR},
      education = #{record.education,jdbcType=VARCHAR},
      school = #{record.school,jdbcType=VARCHAR},
      link_qq = #{record.linkQq,jdbcType=VARCHAR},
      link_wechat = #{record.linkWechat,jdbcType=VARCHAR},
      link_phone = #{record.linkPhone,jdbcType=VARCHAR},
      link_email = #{record.linkEmail,jdbcType=VARCHAR},
      job = #{record.job,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoDO">
    update jianli_user_info
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkAddress != null">
        link_address = #{linkAddress,jdbcType=VARCHAR},
      </if>
      <if test="specialty != null">
        specialty = #{specialty,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        education = #{education,jdbcType=VARCHAR},
      </if>
      <if test="school != null">
        school = #{school,jdbcType=VARCHAR},
      </if>
      <if test="linkQq != null">
        link_qq = #{linkQq,jdbcType=VARCHAR},
      </if>
      <if test="linkWechat != null">
        link_wechat = #{linkWechat,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null">
        link_phone = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="linkEmail != null">
        link_email = #{linkEmail,jdbcType=VARCHAR},
      </if>
      <if test="job != null">
        job = #{job,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoDO">
    update jianli_user_info
    set name = #{name,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=VARCHAR},
      age = #{age,jdbcType=INTEGER},
      address = #{address,jdbcType=VARCHAR},
      link_address = #{linkAddress,jdbcType=VARCHAR},
      specialty = #{specialty,jdbcType=VARCHAR},
      education = #{education,jdbcType=VARCHAR},
      school = #{school,jdbcType=VARCHAR},
      link_qq = #{linkQq,jdbcType=VARCHAR},
      link_wechat = #{linkWechat,jdbcType=VARCHAR},
      link_phone = #{linkPhone,jdbcType=VARCHAR},
      link_email = #{linkEmail,jdbcType=VARCHAR},
      job = #{job,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.jianli.object.UserinfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jianli_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>