<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <head th:fragment="titls">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
        <title>首页</title>
        <meta name="description" content="" />
        <meta name="keywords" content="" />
        <link rel="stylesheet" type="text/css" href="/statics/css/index.css" media="all" />
        <link rel="stylesheet" href="/layui/css/layui.css">
        <script type="text/javascript" src="/layui/layui.js"></script>
    </head>
<body>
        <div  th:fragment="pagination">
            <!--以下为公共部分-->
            <div class="Yarn_Background" style="background-image: url( /statics/images/hai2.jpg);"></div>
            <form class="js-search search-form search-form--modal" method="get" action="/search.html" role="search">
                <div class="search-form__inner">
                    <div>
                        <div id="search-container" class="ajax_search">
                            <form method="get" id="searchform" action="">
                                <div class="filter_container"><input type="text" value="" autocomplete="off" placeholder="请输入文章标题" name="articleTitle" id="search-input" />
                                    <ul id="search_filtered" class="search_filtered"></ul>
                                </div>
                                <button   type="submit"  class="button" id="submit" tabindex="5"   style="display:block;margin:0 auto">搜索</button>
                            </form>
                        </div>
                    </div>
                </div>
            </form>
            <div class="navi" data-aos="fade-down">
                <div class="bt-nav">
                    <div class="line line1"></div>
                    <div class="line line2"></div>
                    <div class="line line3"></div>
                </div>
                <div class="navbar animated fadeInRight">
                    <div class="inner">
                        <nav id="site-navigation" class="main-navigation">
                            <div id="main-menu" class="main-menu-container">
                                <div class="menu-menu-container">
                                    <ul  th:each="column,columnStat:${ parentColumn}"  id="primary-menu" class="menu">
                                        <li th:if="${ columnStat.index == 0}" id="menu-item-17" class="menu-item menu-item-type-custom menu-item-object-custom current-menu-item current_page_item menu-item-home menu-item-17">
                                            <a href="/">首页</a>
                                        </li>
                                        <li id="menu-item-173" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-173">
                                            <a th:href="'/article/parentColumn.html?id='+${ column.id}" th:text="${ column.name}"></a>
                                            <ul class="sub-menu">
                                                <li th:each="childColumn,childColumnStat:${ allChildColumn}"  th:if="${ childColumn.parentId==column.id }"  id="menu-item-165" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-165">
                                                    <a th:href="'/article/column.html?id='+${ childColumn.id}" th:text="${ childColumn.name}"></a>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </nav>
                        <!-- #site-navigation -->
                    </div>
                </div>
            </div>
            <div class="hebin" data-aos="fade-down">
                <i class=" js-toggle-search iconfont">&#xe60e;</i>
            </div>
            <header id="masthead" class="overlay animated from-bottom" itemprop="brand">
                <div class="site-branding text-center">
                    <a href="">
                        <figure>
                            <img class="custom-logo avatar"  th:src="${ user.headPortrait}"/>
                        </figure>
                    </a>
                    <h3 class="blog-description"><p th:text="${ user.signature}"></p></h3>
                </div>
                <!-- .site-branding -->
                <div class="decor-part">
                    <div id="particles-js"></div>
                </div>
                <div class="animation-header">
                    <div class="decor-wrapper">
                        <svg id="header-decor" class="decor bottom" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 100 100" preserveAspectRatio="none">
                            <path class="large left" d="M0 0 L50 50 L0 100" fill="rgba(255,255,255, .1)"></path>
                            <path class="large right" d="M100 0 L50 50 L100 100" fill="rgba(255,255,255, .1)"></path>
                            <path class="medium left" d="M0 100 L50 50 L0 33.3" fill="rgba(255,255,255, .3)"></path>
                            <path class="medium right" d="M100 100 L50 50 L100 33.3" fill="rgba(255,255,255, .3)"></path>
                            <path class="small left" d="M0 100 L50 50 L0 66.6" fill="rgba(255,255,255, .5)"></path>
                            <path class="small right" d="M100 100 L50 50 L100 66.6" fill="rgba(255,255,255, .5)"></path>
                            <path d="M0 99.9 L50 49.9 L100 99.9 L0 99.9" fill="rgba(255,255,255, 1)"></path>
                            <path d="M48 52 L50 49 L52 52 L48 52" fill="rgba(255,255,255, 1)"></path>
                        </svg>
                    </div>
                </div>
            </header>
        </div>

        <div  th:fragment="tail">

            <footer id="footer" class="overlay animated from-top">
                <div class="decor-wrapper">
                    <svg id="footer-decor" class="decor top" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 100 100" preserveAspectRatio="none">
                        <path class="large left" d="M0 0 L50 50 L0 100" fill="rgba(255,255,255, .1)"></path>
                        <path class="large right" d="M100 0 L50 50 L100 100" fill="rgba(255,255,255, .1)"></path>
                        <path class="medium left" d="M0 0 L50 50 L0 66.6" fill="rgba(255,255,255, .3)"></path>
                        <path class="medium right" d="M100 0 L50 50 L100 66.6" fill="rgba(255,255,255, .3)"></path>
                        <path class="small left" d="M0 0 L50 50 L0 33.3" fill="rgba(255,255,255, .5)"></path>
                        <path class="small right" d="M100 0 L50 50 L100 33.3" fill="rgba(255,255,255, .5)"></path>
                        <path d="M0 0 L50 50 L100 0 L0 0" fill="rgba(255,255,255, 1)"></path>
                        <path d="M48 48 L50 51 L52 48 L48 48" fill="rgba(255,255,255, 1)"></path>
                    </svg>
                </div>
                <div class="socialize" data-aos="zoom-in">
                    <!--<li>-->
                        <!--<a title="weibo" class="socialicon" href=""><i class="iconfont" aria-hidden="true">&#xe601;</i></a>-->
                    <!--</li>-->
                    <!--<li class="wechat">-->
                        <!--<a class="socialicon"><i class="iconfont">&#xe601;</i></a>-->
                        <!--<div class="wechatimg"><img th:src="${ user.weibo}"></div>-->
                    <!--</li>-->
                    <li class="wechat">
                        <a class="socialicon"><i class="iconfont">&#xe609;</i></a>
                        <div class="wechatimg"><img th:src="${ user.wechat}"></div>
                    </li>
                    <!--<li>-->
                        <!--<a title="QQ" class="socialicon" href="" target="_blank"><i class="iconfont" aria-hidden="true">&#xe616;</i></a>-->
                    <!--</li>-->
                    <li class="wechat">
                        <a class="socialicon"><i class="iconfont">&#xe616;</i></a>
                        <div class="wechatimg"><img th:src="${ user.qqNumber}"></div>
                    </li>
                </div>
                <div class="cr">

                    <p href="#">联系邮箱:<EMAIL></p>
                    <a href="http://www.miitbeian.gov.cn/" target="view_window"> 渝ICP备18015422号</a>
                </div>
                <script type='text/javascript' src='/statics/js/jquery.min.js'></script>
                <script type='text/javascript' src='/statics/js/plugins.js'></script>
                <script type='text/javascript' src='/statics/js/script.js'></script>
                <script type='text/javascript' src='/statics/js/particles.js'></script>
                <script type='text/javascript' src='/statics/js/aos.js'></script>
            </footer>
        </div>


</body>