/**
*
*
*/

:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #CECFD2; opacity:1; 
}

::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #CECFD2;opacity:1;
}

input:-ms-input-placeholder{
    color: #CECFD2;opacity:1;
}

input::-webkit-input-placeholder{
    color: #CECFD2;opacity:1;
}
html, body { padding: 0; margin: 0; height: 100%; font-size: 16px; background-repeat: no-repeat; background-position: left top; background-color: #242645; color: #fff; font-family: 'Source Sans Pro'; background-size: 100%;
	/*background-image: url(../img/Starry.jpg);*/
}

h1 {
	font-size: 2.8em;
	font-weight: 700;
	letter-spacing: -1px;
	margin: 0.6rem 0; 
}
h1 > span {font-weight: 300; }
h2 {
	font-size: 1.15em;
	font-weight: 300;
	margin: 0.3rem 0; 
}
main {
	width: 95%;
	max-width: 1000px;
	margin: 4em auto;
	opacity: 0; 
}
main.loaded {transition: opacity .25s linear;opacity: 1; }
main header {width: 100%; }
main header > div {width: 50%; }
main header > .left, main header > .right {height: 100%; }
main .loaders {
	width: 100%;
	box-sizing: border-box;
	display: flex;
	flex: 0 1 auto;
	flex-direction: row;
	flex-wrap: wrap;
}
main .loaders .loader {
	box-sizing: border-box;
	display: flex;
	flex: 0 1 auto;
	flex-direction: column;
	flex-grow: 1;
	flex-shrink: 0;
	flex-basis: 25%;
	max-width: 25%;
	height: 200px;
	align-items: center;
	justify-content: center;
}
.J_codeimg
{
        width: 85px;
    height: 36px;
    padding: 3px;
    z-index: 0;
    color:#FFF;
}
.MyLogo{
	top: 0px;
	margin-top: 0px;
	position: absolute;
}