<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  
  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/lib/layui/layui.js" charset="utf-8"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>
  
  <body>
    <div class="x-body">
        <form class="layui-form">


            <div class="layui-form-item">
                <label for="companyName" class="layui-form-label">
                    <span class="x-red">*</span>公司名称
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="companyName" name="companyName" required="" lay-verify="companyName"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="job" class="layui-form-label">
                    <span class="x-red">*</span>任职职位
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="job" name="job" required="" lay-verify="job"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="startTime" class="layui-form-label">
                    <span class="x-red">*</span>入职时间
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="startTime" name="startTime" required="" lay-verify="startTime"
                           autocomplete="off" class="layui-input" placeholder="yyyy-MM-dd">
                </div>
            </div>
            <div class="layui-form-item">
                <label for="endTime" class="layui-form-label">
                    <span class="x-red">*</span>离职时间
                </label>
                <div class="layui-input-inline">
                    <input type="text" id="endTime" name="endTime" required="" lay-verify="endTime"
                           autocomplete="off" class="layui-input" placeholder="yyyy-MM-dd">
                </div>
            </div>

            <div class="layui-form-item">
                <label for="companyImg" class="layui-form-label">
                    公司主图
                </label>
                <input type="hidden"  id="companyImg" name="companyImg"  />
                <input id="fileheadPortrait" type="file" name="fileheadPortrait"/>
                <input type="button"  value="上传" onclick="myFunction(0)" />
                <div id="headPortraitImg"></div>
            </div>

            <div class="layui-form-item">
                <label for="workContent" class="layui-form-label">
                    <span class="x-red">*</span>工作内容
                </label>
                <div class="layui-input-block">
                    <input  placeholder="请输入内容" id="workContent" name="workContent" class="layui-textarea">
                </div>
            </div>
          <div class="layui-form-item">
              <label  class="layui-form-label">
              </label>
              <button  class="layui-btn" lay-filter="add" lay-submit="">
                  增加
              </button>
          </div>
      </form>
    </div>
    <script th:inline="javascript">
      layui.use(['form','layer','laydate'], function(){
          $ = layui.jquery;
        var form = layui.form
        ,layer = layui.layer;
        var laydate = layui.laydate;

          laydate.render({
              elem: '#startTime'
          });
          laydate.render({
              elem: '#endTime'
          });
        //自定义验证规则

          form.verify({
              companyName: function(value){
                  if(value.length <=0){
                      return '公司名称不能为空';
                  }
              },
              job: function(value){
                  if(value.length <=0){
                      return '任职职位不能为空';
                  }
              },
              workContent: function(value){
                  if(value.length <=0){
                      return '任职工作内容不能为空';
                  }
              },
              startTime: function(value){
                  if(value.length <=0){
                      return '入职时间不能为空';
                  }
              },
              endTime: function(value){
                  if(value.length <=0){
                      return '离职时间不能为空';
                  }
              }
          });

        //监听提交
        form.on('submit(add)', function(data){
            $.ajax({
                type: "POST",  //提交方式
                url: "/jianli/work/edit.html",
                dataType: 'json',
                async: false,
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (result) {//返回数据根据结果进行相应的处理
                    if (result.code === 0) {
                        //发异步，把数据提交给php
                        layer.alert("增加成功", {icon: 6},function () {
                            // 获得frame索引
                            var index = parent.layer.getFrameIndex(window.name);
                            //关闭当前frame
                            parent.layer.close(index);
                            //刷新父页面
                            window.parent.location.reload();
                        });
                    } else {
                        layer.alert(result.msg);
                    }
                }
            });


          return false;
        });
        
        
      });
      function myFunction(data){
          var formData = new FormData();
          formData.append('file', $('#fileheadPortrait')[0].files[0]);
          $.ajax({
              type: "POST",  //提交方式
              url: "/headImgUpload.html",
              contentType: false,
              processData: false,
              data: formData,
              success: function (result) {//返回数据根据结果进行相应的处理
                  if (result.code === 0){
                      alert(result.msg);
                      document.getElementById("companyImg").value = result.data;
                      document.getElementById("headPortraitImg").innerHTML="<img src=\""+ result.data+"\" width=\"50\" height=\"50\">";
                  }else {
                      alert(result.msg);
                  }

              }
          });
      }

  </script>
    <script>var _hmt = _hmt || []; (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?b393d153aeb26b46e9431fabaf0f6190";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();</script>
  </body>

</html>