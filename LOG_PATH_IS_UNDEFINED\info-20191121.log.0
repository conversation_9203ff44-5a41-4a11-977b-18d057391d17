2019-11-21 15:46:28 [main] INFO  com.hongshen.boke.BokeApplication -Starting BokeApplication on MJVCNI24UPBAJO7 with PID 17472 (started by Administrator in D:\我的码云项目\博客\persion) 
2019-11-21 15:46:28 [main] DEBUG com.hongshen.boke.BokeApplication -Running with Spring Boot v2.0.5.RELEASE, Spring v5.0.9.RELEASE 
2019-11-21 15:46:28 [main] INFO  com.hongshen.boke.BokeApplication -The following profiles are active: my 
2019-11-21 15:46:28 [main] INFO  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3b8b4846: startup date [Thu Nov 21 15:46:28 CST 2019]; root of context hierarchy 
2019-11-21 15:46:31 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate -Multiple Spring Data modules found, entering strict repository configuration mode! 
2019-11-21 15:46:32 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -Bean 'org.springframework.kafka.annotation.KafkaBootstrapConfiguration' of type [org.springframework.kafka.annotation.KafkaBootstrapConfiguration$$EnhancerBySpringCGLIB$$d0835bb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2019-11-21 15:46:32 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker -Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$fcd14a2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying) 
2019-11-21 15:46:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -Tomcat initialized with port(s): 8050 (http) 
2019-11-21 15:46:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol -Initializing ProtocolHandler ["http-nio-8050"] 
2019-11-21 15:46:34 [main] INFO  o.a.catalina.core.StandardService -Starting service [Tomcat] 
2019-11-21 15:46:34 [main] INFO  o.a.catalina.core.StandardEngine -Starting Servlet Engine: Apache Tomcat/8.5.34 
2019-11-21 15:46:34 [localhost-startStop-1] INFO  o.a.c.core.AprLifecycleListener -The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [E:\anzhuangmulu\java\jdk1.8.0_162\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;E:\anzhuangmulu\Xshell\;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;E:\anzhuangmulu\java\jdk1.8.0_162\bin;E:\anzhuangmulu\java\jdk1.8.0_162\jre\bin;C:\Program Files\TortoiseSVN\bin;E:\anzhuangmulu\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;.] 
2019-11-21 15:46:34 [localhost-startStop-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] -Initializing Spring embedded WebApplicationContext 
2019-11-21 15:46:34 [localhost-startStop-1] INFO  o.s.web.context.ContextLoader -Root WebApplicationContext: initialization completed in 5925 ms 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.ServletRegistrationBean -Servlet dispatcherServlet mapped to [/] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.ServletRegistrationBean -Servlet statViewServlet mapped to [/druid/*] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.FilterRegistrationBean -Mapping filter: 'characterEncodingFilter' to: [/*] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.FilterRegistrationBean -Mapping filter: 'hiddenHttpMethodFilter' to: [/*] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.FilterRegistrationBean -Mapping filter: 'httpPutFormContentFilter' to: [/*] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.FilterRegistrationBean -Mapping filter: 'requestContextFilter' to: [/*] 
2019-11-21 15:46:35 [localhost-startStop-1] INFO  o.s.b.w.s.FilterRegistrationBean -Mapping filter: 'webStatFilter' to urls: [/*] 
2019-11-21 15:46:35 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure -Init DruidDataSource 
2019-11-21 15:46:36 [main] INFO  c.alibaba.druid.pool.DruidDataSource -{dataSource-1} inited 
2019-11-21 15:46:39 [main] INFO  o.s.w.s.h.SimpleUrlHandlerMapping -Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler] 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerAdapter -Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3b8b4846: startup date [Thu Nov 21 15:46:28 CST 2019]; root of context hierarchy 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/delete.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.ArticleController.delete(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/queryList.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.response.article.ArticleListResponse>> com.hongshen.boke.controller.ArticleController.list(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/query.html]}" onto public java.lang.String com.hongshen.boke.controller.ArticleController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.ArticleController.edit(com.hongshen.boke.dao.object.ArticleDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/list.html]}" onto public java.lang.String com.hongshen.boke.controller.ArticleController.article() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/article/article-edit.html]}" onto public java.lang.String com.hongshen.boke.controller.ArticleController.articleEdit() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/activemq/send]}" onto public java.lang.String com.hongshen.boke.controller.avtivemq.ProviderController.send() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/boke.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.index(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/article/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.response.article.ArticleListResponse>> com.hongshen.boke.controller.before.MyBokeController.list(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/search.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.search(org.springframework.ui.Model,java.lang.String) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/tourist/registered.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.registered() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/tourist/add.html]}" onto public com.hongshen.boke.response.ResultResponse com.hongshen.boke.controller.before.MyBokeController.addTourist(javax.servlet.http.HttpServletRequest,java.util.Map<java.lang.String, java.lang.String>) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/article/detail.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.queryArticle(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/toutist/login.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.login() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/article/parentColumn.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.queryArticleForParentColumn(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/article/column.html]}" onto public java.lang.String com.hongshen.boke.controller.before.MyBokeController.queryArticleForChildColumn(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/bombing/index]}" onto public void com.hongshen.boke.controller.bombing.BombingController.index() throws java.awt.AWTException 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/delete.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.ColumnController.delete(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/queryList.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.object.ColumnDO>> com.hongshen.boke.controller.ColumnController.list(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/query.html]}" onto public java.lang.String com.hongshen.boke.controller.ColumnController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.ColumnController.edit(com.hongshen.boke.dao.object.ColumnDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/list.html]}" onto public java.lang.String com.hongshen.boke.controller.ColumnController.column() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/column-edit.html]}" onto public java.lang.String com.hongshen.boke.controller.ColumnController.columnEdit() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/queryChildColumn.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.object.ColumnDO>> com.hongshen.boke.controller.ColumnController.queryChildColumn(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/column/queryParentColumn.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.object.ColumnDO>> com.hongshen.boke.controller.ColumnController.queryParentColumn(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/index.html]}" onto public java.lang.String com.hongshen.boke.controller.IndexController.main(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/welcome.html]}" onto public java.lang.String com.hongshen.boke.controller.IndexController.welcome() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/login.html]}" onto public java.lang.String com.hongshen.boke.controller.IndexController.managerIndex() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/out.html]}" onto public java.lang.String com.hongshen.boke.controller.LoginController.out(javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/logins.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.LoginController.logins(com.hongshen.boke.dao.object.UserDO,javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/userDetails.html]}" onto public java.lang.String com.hongshen.boke.controller.LoginController.userDetails(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/shiro/login]}" onto public java.lang.String com.hongshen.boke.controller.shiro.ShiroController.login(java.lang.String,java.lang.String) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/shiro/a]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.response.article.ArticleListResponse>> com.hongshen.boke.controller.shiro.ShiroController.logins() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/toutist/delete.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.TouristController.delete(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/toutist/queryList.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.object.TouristDO>> com.hongshen.boke.controller.TouristController.list(java.lang.Integer,java.lang.Integer,java.lang.String) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/toutist/query.html]}" onto public java.lang.String com.hongshen.boke.controller.TouristController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/toutist/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.TouristController.edit(com.hongshen.boke.dao.object.TouristDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/toutist/login.html]}" onto public com.hongshen.boke.response.ResultResponse<com.hongshen.boke.dao.object.TouristDO> com.hongshen.boke.controller.TouristController.login(javax.servlet.http.HttpServletRequest,java.util.Map<java.lang.String, java.lang.String>) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/tourist/list.html]}" onto public java.lang.String com.hongshen.boke.controller.TouristController.tourist() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/tourist-edit.html]}" onto public java.lang.String com.hongshen.boke.controller.TouristController.touristEdit() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/delete.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.UserController.delete(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/queryList.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.object.UserDO>> com.hongshen.boke.controller.UserController.list(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/query.html]}" onto public java.lang.String com.hongshen.boke.controller.UserController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/list.html]}" onto public java.lang.String com.hongshen.boke.controller.UserController.user() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.controller.UserController.edit(com.hongshen.boke.dao.object.UserDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/manager/user/user-edit.html]}" onto public java.lang.String com.hongshen.boke.controller.UserController.userEdit() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/headImgUpload.html]}" onto public com.hongshen.boke.response.ResultResponse com.hongshen.boke.controller.UserController.headImgUpload(org.springframework.ui.Model,org.springframework.web.multipart.MultipartFile,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/index.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.AppraisalController.index() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/query.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.AppraisalController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/appraisal-edit.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.AppraisalController.info() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.AppraisalController.edit(com.hongshen.boke.dao.jianli.object.AppraisalDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/del.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.AppraisalController.del(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/appraisal/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.jianli.object.AppraisalDO>> com.hongshen.boke.jianli.controller.AppraisalController.queryList(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/]}" onto public java.lang.String com.hongshen.boke.jianli.controller.before.JianliIndexController.index(org.springframework.ui.Model) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/sendEmail]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.before.JianliIndexController.index(com.hongshen.boke.request.email.EmailRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/download]}" onto public void com.hongshen.boke.jianli.controller.before.JianliIndexController.download(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/index.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.ProjectController.index() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/query.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.ProjectController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/project-edit.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.ProjectController.info() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.ProjectController.edit(com.hongshen.boke.dao.jianli.object.ProjectDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/del.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.ProjectController.del(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/project/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.jianli.object.ProjectDO>> com.hongshen.boke.jianli.controller.ProjectController.queryList(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/index.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.SkillController.index() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/query.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.SkillController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/skill-edit.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.SkillController.info() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.SkillController.edit(com.hongshen.boke.dao.jianli.object.SkillDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/del.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.SkillController.del(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/skill/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.jianli.object.SkillDO>> com.hongshen.boke.jianli.controller.SkillController.queryList(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/index.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.UserInfoController.index() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/query.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.UserInfoController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/userinfo-edit.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.UserInfoController.info() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.UserInfoController.edit(com.hongshen.boke.dao.jianli.object.UserinfoDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/del.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.UserInfoController.del(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.jianli.object.UserinfoDO>> com.hongshen.boke.jianli.controller.UserInfoController.queryList(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/jianli-upload.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.UserInfoController.upload() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/userinfo/upload.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.UserInfoController.uploadFile(org.springframework.web.multipart.MultipartFile) throws java.io.IOException 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/index.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.WorkController.index() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/query.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.WorkController.query(org.springframework.ui.Model,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/work-edit.html]}" onto public java.lang.String com.hongshen.boke.jianli.controller.WorkController.info() 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/edit.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.WorkController.edit(com.hongshen.boke.dao.jianli.object.WorkDO) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/del.html]}" onto public com.hongshen.boke.response.ResultResponse<java.lang.String> com.hongshen.boke.jianli.controller.WorkController.del(java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/jianli/work/list.html]}" onto public com.hongshen.boke.response.ResultResponse<java.util.List<com.hongshen.boke.dao.jianli.object.WorkDO>> com.hongshen.boke.jianli.controller.WorkController.queryList(java.lang.Integer,java.lang.Integer) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.m.m.a.RequestMappingHandlerMapping -Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.h.SimpleUrlHandlerMapping -Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler] 
2019-11-21 15:46:40 [main] INFO  o.s.w.s.h.SimpleUrlHandlerMapping -Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler] 
2019-11-21 15:46:45 [main] INFO  o.s.j.e.a.AnnotationMBeanExporter -Registering beans for JMX exposure on startup 
2019-11-21 15:46:45 [main] INFO  o.s.j.e.a.AnnotationMBeanExporter -Bean with name 'statFilter' has been autodetected for JMX exposure 
2019-11-21 15:46:45 [main] INFO  o.s.j.e.a.AnnotationMBeanExporter -Bean with name 'dataSource' has been autodetected for JMX exposure 
2019-11-21 15:46:45 [main] INFO  o.s.j.e.a.AnnotationMBeanExporter -Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper] 
2019-11-21 15:46:45 [main] INFO  o.s.j.e.a.AnnotationMBeanExporter -Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter] 
2019-11-21 15:46:45 [main] INFO  o.s.c.s.DefaultLifecycleProcessor -Starting beans in phase 2147483547 
2019-11-21 15:46:45 [main] INFO  o.s.c.s.DefaultLifecycleProcessor -Starting beans in phase 2147483647 
2019-11-21 15:46:45 [main] INFO  o.a.activemq.broker.BrokerService -Using Persistence Adapter: MemoryPersistenceAdapter 
2019-11-21 15:46:45 [JMX connector] INFO  o.a.a.broker.jmx.ManagementContext -JMX consoles can connect to service:jmx:rmi:///jndi/rmi://localhost:1099/jmxrmi 
2019-11-21 15:46:45 [main] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, ID:MJVCNI24UPBAJO7-53294-1574322405558-0:1) is starting 
2019-11-21 15:46:45 [main] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, ID:MJVCNI24UPBAJO7-53294-1574322405558-0:1) started 
2019-11-21 15:46:45 [main] INFO  o.a.activemq.broker.BrokerService -For help or more information please see: http://activemq.apache.org 
2019-11-21 15:46:46 [main] INFO  o.a.a.broker.TransportConnector -Connector vm://localhost started 
2019-11-21 15:46:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol -Starting ProtocolHandler ["http-nio-8050"] 
2019-11-21 15:46:46 [main] INFO  o.a.tomcat.util.net.NioSelectorPool -Using a shared selector for servlet write/read 
2019-11-21 15:46:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -Tomcat started on port(s): 8050 (http) with context path '' 
2019-11-21 15:46:46 [main] INFO  com.hongshen.boke.BokeApplication -Started BokeApplication in 20.182 seconds (JVM running for 42.415) 
2019-11-21 15:46:49 [http-nio-8050-exec-9] INFO  o.a.tomcat.util.http.parser.Cookie -A cookie header was received [1574242827] that contained an invalid cookie. That cookie will be ignored.Note: further occurrences of this error will be logged at DEBUG level. 
2019-11-21 15:46:49 [http-nio-8050-exec-9] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] -Initializing Spring FrameworkServlet 'dispatcherServlet' 
2019-11-21 15:46:49 [http-nio-8050-exec-9] INFO  o.s.web.servlet.DispatcherServlet -FrameworkServlet 'dispatcherServlet': initialization started 
2019-11-21 15:46:49 [http-nio-8050-exec-9] INFO  o.s.web.servlet.DispatcherServlet -FrameworkServlet 'dispatcherServlet': initialization completed in 68 ms 
2019-11-21 15:46:50 [http-nio-8050-exec-9] INFO  c.h.b.c.before.MyBokeController -域名路径为=======127.0.0.1================================ 
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.selectByExample -==>  Preparing: select id, ip, interface_name, count, update_time from jianli_statistics WHERE ( ip = ? and interface_name = ? )  
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.selectByExample -==> Parameters: 127.0.0.1(String), /boke.html(String) 
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.selectByExample -<==      Total: 1 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.UserMapper.selectByExample -==>  Preparing: select id, username, password, name, head_portrait, wechat, qq_number, weibo, signature from boke_user  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.UserMapper.selectByExample -==> Parameters:  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.UserMapper.selectByExample -<==      Total: 1 
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.updateByPrimaryKeySelective -==>  Preparing: update jianli_statistics SET ip = ?, interface_name = ?, count = ?, update_time = ? where id = ?  
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.updateByPrimaryKeySelective -==> Parameters: 127.0.0.1(String), /boke.html(String), 3(Integer), 2019-11-21 15:46:51.464(Timestamp), 286(Integer) 
2019-11-21 15:46:51 [DefaultMessageListenerContainer-1] DEBUG c.h.b.d.j.m.S.updateByPrimaryKeySelective -<==    Updates: 1 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.M.list_COUNT -==>  Preparing: SELECT count(0) FROM boke_article ba LEFT JOIN boke_user bu ON ba.user_id = bu.id LEFT JOIN boke_column bc ON ba.column_id = bc.id WHERE 1 = 1  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.M.list_COUNT -==> Parameters:  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.M.list_COUNT -<==      Total: 1 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.MyArticleMapper.list -==>  Preparing: select ba.id, ba.title,ba.creat_time as creatTime,bc.name as columnName ,bu.name as uName,ba.description as description,bc.parent_name as parentName from boke_article ba left join boke_user bu on ba.user_id=bu.id left join boke_column bc on ba.column_id=bc.id where 1=1 order by ba.creat_time LIMIT ?  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.MyArticleMapper.list -==> Parameters: 10(Integer) 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.m.MyArticleMapper.list -<==      Total: 10 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -==>  Preparing: select id, name, link, description, parent_id, parent_name from boke_column WHERE ( parent_id is null )  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -==> Parameters:  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -<==      Total: 1 
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -==>  Preparing: select id, name, link, description, parent_id, parent_name from boke_column WHERE ( parent_id is not null )  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -==> Parameters:  
2019-11-21 15:46:51 [http-nio-8050-exec-9] DEBUG c.h.b.d.m.C.selectByExample -<==      Total: 3 
2019-11-21 15:53:33 [ActiveMQ ShutdownHook] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, ID:MJVCNI24UPBAJO7-53294-1574322405558-0:1) is shutting down 
2019-11-21 15:53:33 [Thread-64] INFO  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3b8b4846: startup date [Thu Nov 21 15:46:28 CST 2019]; root of context hierarchy 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] WARN  o.s.j.l.DefaultMessageListenerContainer -Setup of JMS message listener invoker failed for destination 'email' - trying to recover. Cause: peer (vm://localhost#1) stopped. 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
javax.management.InstanceAlreadyExistsException: org.apache.activemq:type=Broker,brokerName=localhost
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:437)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1898)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:966)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:900)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:324)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:522)
	at org.apache.activemq.broker.jmx.ManagementContext.registerMBean(ManagementContext.java:409)
	at org.apache.activemq.broker.jmx.AnnotatedMBean.registerMBean(AnnotatedMBean.java:91)
	at org.apache.activemq.broker.BrokerService.startManagementContext(BrokerService.java:2627)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:621)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutting down 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] WARN  o.s.j.l.DefaultMessageListenerContainer -Setup of JMS message listener invoker failed for destination 'testQueue' - trying to recover. Cause: peer (vm://localhost#3) stopped. 
2019-11-21 15:53:34 [ActiveMQ ShutdownHook] INFO  o.a.a.broker.TransportConnector -Connector vm://localhost stopped 
2019-11-21 15:53:34 [JMX connector] WARN  o.a.a.broker.jmx.ManagementContext -Failed to start JMX connector Cannot bind to URL [rmi://localhost:1099/jmxrmi]: javax.naming.NameAlreadyBoundException: jmxrmi [Root exception is java.rmi.AlreadyBoundException: jmxrmi]. Will restart management to re-create JMX connector, trying to remedy this issue. 
2019-11-21 15:53:34 [Thread-64] WARN  o.s.c.a.ClassPathScanningCandidateComponentProvider -No MyBatis mapper was found in '[com.hongshen.boke.dao.**]' package. Please check your configuration. 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] WARN  o.s.j.l.DefaultMessageListenerContainer -Setup of JMS message listener invoker failed for destination 'count' - trying to recover. Cause: peer (vm://localhost#5) stopped. 
2019-11-21 15:53:34 [ActiveMQ ShutdownHook] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, ID:MJVCNI24UPBAJO7-53294-1574322405558-0:1) uptime 6 minutes 
2019-11-21 15:53:34 [ActiveMQ ShutdownHook] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, ID:MJVCNI24UPBAJO7-53294-1574322405558-0:1) is shutdown 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) uptime 0.065 seconds 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutdown 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'email' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: javax.management.InstanceAlreadyExistsException: org.apache.activemq:type=Broker,brokerName=localhost 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Using Persistence Adapter: MemoryPersistenceAdapter 
2019-11-21 15:53:34 [JMX connector] INFO  o.a.a.broker.jmx.ManagementContext -JMX consoles can connect to service:jmx:rmi:///jndi/rmi://localhost:1099/jmxrmi 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:211)
	at org.apache.activemq.broker.BrokerService.addShutdownHook(BrokerService.java:2544)
	at org.apache.activemq.broker.BrokerService.doStartBroker(BrokerService.java:740)
	at org.apache.activemq.broker.BrokerService.startBroker(BrokerService.java:733)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:636)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutting down 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) uptime 0.015 seconds 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutdown 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'count' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: java.lang.IllegalStateException: Shutdown in progress 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Using Persistence Adapter: MemoryPersistenceAdapter 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.a.activemq.broker.BrokerService -Failed to start Apache ActiveMQ (localhost, null) 
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.add(ApplicationShutdownHooks.java:66)
	at java.lang.Runtime.addShutdownHook(Runtime.java:211)
	at org.apache.activemq.broker.BrokerService.addShutdownHook(BrokerService.java:2544)
	at org.apache.activemq.broker.BrokerService.doStartBroker(BrokerService.java:740)
	at org.apache.activemq.broker.BrokerService.startBroker(BrokerService.java:733)
	at org.apache.activemq.broker.BrokerService.start(BrokerService.java:636)
	at org.apache.activemq.transport.vm.VMTransportFactory.doCompositeConnect(VMTransportFactory.java:127)
	at org.apache.activemq.transport.vm.VMTransportFactory.doConnect(VMTransportFactory.java:56)
	at org.apache.activemq.transport.TransportFactory.connect(TransportFactory.java:65)
	at org.apache.activemq.ActiveMQConnectionFactory.createTransport(ActiveMQConnectionFactory.java:331)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:346)
	at org.apache.activemq.ActiveMQConnectionFactory.createActiveMQConnection(ActiveMQConnectionFactory.java:304)
	at org.apache.activemq.ActiveMQConnectionFactory.createConnection(ActiveMQConnectionFactory.java:244)
	at org.springframework.jms.support.JmsAccessor.createConnection(JmsAccessor.java:196)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.createSharedConnection(AbstractJmsListeningContainer.java:417)
	at org.springframework.jms.listener.AbstractJmsListeningContainer.refreshSharedConnection(AbstractJmsListeningContainer.java:402)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.refreshConnectionUntilSuccessful(DefaultMessageListenerContainer.java:940)
	at org.springframework.jms.listener.DefaultMessageListenerContainer.recoverAfterListenerSetupFailure(DefaultMessageListenerContainer.java:914)
	at org.springframework.jms.listener.DefaultMessageListenerContainer$AsyncMessageListenerInvoker.run(DefaultMessageListenerContainer.java:1098)
	at java.lang.Thread.run(Thread.java:748)
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutting down 
2019-11-21 15:53:34 [JMX connector] INFO  o.a.a.broker.jmx.ManagementContext -JMX consoles can connect to service:jmx:rmi:///jndi/rmi://localhost:1099/jmxrmi 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) uptime 0.012 seconds 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] INFO  o.a.activemq.broker.BrokerService -Apache ActiveMQ 5.15.6 (localhost, null) is shutdown 
2019-11-21 15:53:34 [DefaultMessageListenerContainer-1] ERROR o.s.j.l.DefaultMessageListenerContainer -Could not refresh JMS Connection for destination 'testQueue' - retrying using FixedBackOff{interval=5000, currentAttempts=0, maxAttempts=unlimited}. Cause: Could not create Transport. Reason: java.lang.IllegalStateException: Shutdown in progress 
2019-11-21 15:53:34 [Thread-64] INFO  o.s.c.s.DefaultLifecycleProcessor -Stopping beans in phase 2147483647 
2019-11-21 15:53:34 [Thread-64] INFO  o.s.c.s.DefaultLifecycleProcessor -Stopping beans in phase 2147483547 
2019-11-21 15:53:34 [Thread-64] INFO  o.s.c.s.DefaultLifecycleProcessor -Stopping beans in phase 0 
2019-11-21 15:53:34 [Thread-64] INFO  o.s.j.e.a.AnnotationMBeanExporter -Unregistering JMX-exposed beans on shutdown 
2019-11-21 15:53:34 [Thread-64] INFO  o.s.j.e.a.AnnotationMBeanExporter -Unregistering JMX-exposed beans 
2019-11-21 15:53:34 [Thread-64] INFO  c.alibaba.druid.pool.DruidDataSource -{dataSource-1} closed 
