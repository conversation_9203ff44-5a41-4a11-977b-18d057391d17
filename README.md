# java个人在线简历博客一体化

#### 介绍
本项目是个人在线简历的和个人博客一体化的项目。前端可进行个人简历信息的展示、下载，个人博客文章的浏览、游客登录留言等，后端可进行个人简历的上传、个人展示信息的修改，博客可进行文章的发布、游客的管理等。项目整合了shiro、redis、activemq、kafka、163邮件、阿里云oss。目前activemq整合到业务中用于发送异步发送163邮件服务，oss用于存储图片，其它目前只做了简单了demo，尚未整合到业务中。
后台用户上传的简历附件存储在项目src同级目录下static/file目录下。

#### 软件架构
springboot+mybatis+activemq+thymeleaf+layui



#### 安装教程
1.生成数据表（执行/src/main/resources目录下的my_jianli.sql文件）
2.表生成之后已经可以启动项目，如需使用邮件功能需要配置/src/main/resources的config.properties文件和application.yml里面关于activemq的相关配置


#### 项目链接

在线简历地址：http://122.51.167.121:8050/

博客首页地址：http://122.51.167.121:8050/boke.html


