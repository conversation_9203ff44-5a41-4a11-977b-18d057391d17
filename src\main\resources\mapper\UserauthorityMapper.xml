<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.mapper.UserauthorityMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.object.UserauthorityDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="authority_id" jdbcType="INTEGER" property="authorityId" />
    <result column="authority_type" jdbcType="INTEGER" property="authorityType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, authority_id, authority_type
  </sql>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.object.UserauthorityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_user_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from boke_user_authority
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from boke_user_authority
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.object.UserauthorityExample">
    delete from boke_user_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.object.UserauthorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_user_authority (authority_id, authority_type)
    values (#{authorityId,jdbcType=INTEGER}, #{authorityType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.object.UserauthorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_user_authority
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorityId != null">
        authority_id,
      </if>
      <if test="authorityType != null">
        authority_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorityId != null">
        #{authorityId,jdbcType=INTEGER},
      </if>
      <if test="authorityType != null">
        #{authorityType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.object.UserauthorityExample" resultType="java.lang.Long">
    select count(*) from boke_user_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update boke_user_authority
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.authorityId != null">
        authority_id = #{record.authorityId,jdbcType=INTEGER},
      </if>
      <if test="record.authorityType != null">
        authority_type = #{record.authorityType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update boke_user_authority
    set id = #{record.id,jdbcType=INTEGER},
      authority_id = #{record.authorityId,jdbcType=INTEGER},
      authority_type = #{record.authorityType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.object.UserauthorityDO">
    update boke_user_authority
    <set>
      <if test="authorityId != null">
        authority_id = #{authorityId,jdbcType=INTEGER},
      </if>
      <if test="authorityType != null">
        authority_type = #{authorityType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.object.UserauthorityDO">
    update boke_user_authority
    set authority_id = #{authorityId,jdbcType=INTEGER},
      authority_type = #{authorityType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.object.UserauthorityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_user_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>