<!DOCTYPE html>
<html>

  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <script type="text/javascript" src="/layui/layui.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>

  <body>
    <div class="x-body">
        <form class="layui-form">
          <div class="layui-form-item">
              <label for="title" class="layui-form-label">
                  <span class="x-red">*</span>文章主题
              </label>
              <div class="layui-input-inline">
                  <input type="text" id="title" name="title" required="" lay-verify="required"
                  autocomplete="off" class="layui-input">
              </div>
          </div>
          <!--<div class="layui-form-item">-->
              <!--<label for="columnId" class="layui-form-label">-->
                  <!--<span class="x-red">*</span>文章栏目-->
              <!--</label>-->
              <!--<div class="layui-input-inline">-->
                  <!--<input type="text" id="columnId" name="columnId" required="" lay-verify="required"-->
                  <!--autocomplete="off" class="layui-input">-->
              <!--</div>-->
          <!--</div>-->
            <div class="layui-form-item">
                <label  class="layui-form-label">
                    <span class="x-red">*</span>栏目名称
                </label>
                <div class="layui-input-inline">
                    <select name="parentId" lay-filter="parentId" class="tid">
                        <option value="">请选择栏目</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="columnId" lay-filter="columnId" >
                        <option value="">请选择子栏目</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label for="content" class="layui-form-label">
                    文章内容
                </label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入内容" id="content" name="content" class="layui-textarea"></textarea>
                </div>
            </div>
          <div class="layui-form-item layui-form-text">
              <label for="description" class="layui-form-label">
                  文章描述
              </label>
              <div class="layui-input-block">
                  <textarea placeholder="请输入内容" id="description" name="description" class="layui-textarea"></textarea>
              </div>
          </div>
          <div class="layui-form-item">
              <label  class="layui-form-label">
              </label>
              <button  class="layui-btn" lay-filter="add" lay-submit="">
                  增加
              </button>
          </div>

      </form>
    </div>
    <script>
        layui.use(['form','layer','layedit'], function(){
           var $ = layui.jquery;
          var form = layui.form
          ,layer = layui.layer;
          var layedit = layui.layedit;

            layedit.set({
                uploadImage: {
                    url: '/headImgUpload.html?status=1' //接口url
                    ,type: 'post' //默认post
                }
            });
        //注意：layedit.set 一定要放在 build 前面，否则配置全局接口将无效。
          layedit.build('content'); //建立编辑器


            //监听提交
            form.on('submit(add)', function(data){
                $.ajax({
                    type: "POST",  //提交方式
                    url: "/manager/article/edit.html",
                    dataType: 'json',
                    async: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data.field),
                    success: function (result) {//返回数据根据结果进行相应的处理
                        if (result.code === 0) {
                            //发异步，把数据提交给php
                            layer.alert("添加成功", {icon: 6},function () {
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                //刷新父页面
                                window.parent.location.reload();
                            });
                        } else {
                            layer.alert(result.msg);
                        }
                    }
                });
                return false;
            });

            $(document).ready(function(){
                $.ajax({
                    type: "GET",  //提交方式
                    url: "/manager/column/queryParentColumn.html",//路径
                    dataType : 'json',
                    contentType : 'application/json',
                    success: function (result) {
                        if(!result.success){layer.alert(result.msg,{icon: 2,skin: 'layer-ext-moon'});return;}
                        var list = result.data;
                        if (list == null||list.length==0) return;
                        var opts = "";
                        for (var i = 0; i < list.length; i++) {
                            opts += "<option   value='" + list[i].id + "'>" + list[i].name + "</option>";
                        }

                        $("select[name='parentId']").append(opts);
                        form.render('select');
                    }
                });
            });

        });
    </script>
    <script>
        layui.use(['form','layedit'], function(){
            var form = layui.form,
                layedit = layui.layedit,
                $ = layui.jquery,
                layer = layui.layer;


            form.on('select(parentId)', function(data){
                console.log(data.value); //得到被选中的值
                $.ajax({
                    url:"/manager/column/queryChildColumn.html?pId="+data.value,
                    // data:{tid:data.value},
                    type:"GET",
                    success:function(result){
                        $("select[name='columnId']").empty();
                        if(!result.success){layer.alert(result.msg,{icon: 2,skin: 'layer-ext-moon'});return;}
                        var list = result.data;
                        if (list == null||list.length==0){
                            $("select[name='columnId']").append("<option>暂无子栏目</option>");
                            form.render('select');
                            return;
                        }
                        var opts = "";
                        for (var i = 0; i < list.length; i++) {
                            opts += "<option   value='" + list[i].id + "'>" + list[i].name + "</option>";
                        }
                        $("select[name='columnId']").append(opts);
                        form.render('select');
                    }
                });

            });
        });

    </script>

  </body>

</html>