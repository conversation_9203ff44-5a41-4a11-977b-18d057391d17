<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

  <head>
    <meta charset="UTF-8">
    <title>欢迎页面-X-admin2.0</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/css/font.css">
    <link rel="stylesheet" href="/css/xadmin.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <script type="text/javascript" src="https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js"></script>
    <script type="text/javascript" src="/js/xadmin.js"></script>
    <script type="text/javascript" src="/layui/layui.js"></script>
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
  </head>

  <body>
    <div class="x-body">
        <form class="layui-form">
            <input type="hidden"  th:value="${ user.id}" id="id"  name="id"  autocomplete="off" class="layui-input">
          <div class="layui-form-item">
              <label for="username" class="layui-form-label">
                  <span class="x-red">*</span>登录账号
              </label>
              <div class="layui-input-inline">
                  <input th:value="${ user.username}" type="text" id="username" name="username" required="" lay-verify="required"
                  autocomplete="off" class="layui-input" disabled>
              </div>
          </div>
            <div class="layui-form-item ">
                <label for="name" class="layui-form-label">
                    用户昵称
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ user.name}" type="text" id="name" name="name" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" disabled>
                </div>
            </div>

          <div class="layui-form-item">
              <label for="headPortrait" class="layui-form-label">
                  头像
              </label>
              <div class="layui-input-inline">
                  <input th:value="${ user.headPortrait}" type="text" id="headPortrait" name="headPortrait" required="" lay-verify="required"
                         autocomplete="off" class="layui-input" disabled>
              </div>
          </div>
            <div class="layui-form-item">
                <label for="wechat" class="layui-form-label">
                    <span class="x-red">*</span>微信
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ user.wechat}" type="text" id="wechat" name="wechat" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" disabled>
                </div>
            </div>
            <div class="layui-form-item ">
                <label for="qqNumber" class="layui-form-label">
                    QQ
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ user.qqNumber}" type="text" id="qqNumber" name="qqNumber" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" disabled>
                </div>
            </div>
            <div class="layui-form-item ">
                <label for="weibo" class="layui-form-label">
                    微博
                </label>
                <div class="layui-input-inline">
                    <input th:value="${ user.weibo}" type="text" id="weibo" name="weibo" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" disabled>
                </div>
            </div>
            <div class="layui-form-item">
                <label for="signature" class="layui-form-label">
                   签名
                </label>
                <div class="layui-input-block">
                    <input th:value="${ user.signature}" type="text" id="signature" name="signature" required="" lay-verify="required"
                           autocomplete="off" class="layui-input" disabled>
                </div>
            </div>


      </form>
    </div>
    <script>
        layui.use(['form','layer'], function(){
           var $ = layui.jquery;
          var form = layui.form
          ,layer = layui.layer;

          //自定义验证规则
          form.verify({
            nikename: function(value){
              if(value.length < 5){
                return '昵称至少得5个字符啊';
              }
            }
            ,repass: function(value){
                if($('#password').val()!=$('#passwd').val()){
                    return '两次密码不一致';
                }
            }
          });

            //监听提交
            form.on('submit(add)', function(data){
                $.ajax({
                    type: "POST",  //提交方式
                    url: "/manager/user/edit.html",
                    dataType: 'json',
                    async: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data.field),
                    success: function (result) {//返回数据根据结果进行相应的处理
                        if (result.code === 0) {
                            //发异步，把数据提交给php
                            layer.alert("修改成功", {icon: 6},function () {
                                // 获得frame索引
                                var index = parent.layer.getFrameIndex(window.name);
                                //关闭当前frame
                                parent.layer.close(index);
                                //刷新父页面
                                window.parent.location.reload();
                            });
                        } else {
                            layer.alert(result.msg);
                        }
                    }
                });
                return false;
            });


        });
    </script>
  </body>

</html>