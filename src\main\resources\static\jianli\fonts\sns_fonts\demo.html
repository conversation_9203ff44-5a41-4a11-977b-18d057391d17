
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <link rel="stylesheet" href="iconfont.css">
</head>
<body>
    <div class="main">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont">&#xe66e;</i>
                    <div class="name">微博</div>
                    <div class="code">&amp;#xe66e;</div>
                    <div class="fontclass">.weibo</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe629;</i>
                    <div class="name">qq</div>
                    <div class="code">&amp;#xe629;</div>
                    <div class="fontclass">.qq</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe66b;</i>
                    <div class="name">人人</div>
                    <div class="code">&amp;#xe66b;</div>
                    <div class="fontclass">.renren</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe616;</i>
                    <div class="name">空间</div>
                    <div class="code">&amp;#xe616;</div>
                    <div class="fontclass">.kongjian</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xf0096;</i>
                    <div class="name">空间</div>
                    <div class="code">&amp;#xf0096;</div>
                    <div class="fontclass">.kongjian1</div>
                </li>
            
        </ul>


        <div class="helps">
            第一步：使用font-face声明字体
            <pre>
@font-face {font-family: 'iconfont';
    src: url('iconfont.eot'); /* IE9*/
    src: url('iconfont.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('iconfont.woff') format('woff'), /* chrome、firefox */
    url('iconfont.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url('iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
}
</pre>
第二步：定义使用iconfont的样式
            <pre>
.iconfont{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;}
</pre>
第三步：挑选相应图标并获取字体编码，应用于页面
<pre>
&lt;i class="iconfont"&gt;&amp;#x33;&lt;/i&gt;
</pre>
        </div>

    </div>
</body>
</html>
