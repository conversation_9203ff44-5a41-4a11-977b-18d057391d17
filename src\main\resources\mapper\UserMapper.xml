<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongshen.boke.dao.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.hongshen.boke.dao.object.UserDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="head_portrait" jdbcType="VARCHAR" property="headPortrait" />
    <result column="wechat" jdbcType="VARCHAR" property="wechat" />
    <result column="qq_number" jdbcType="VARCHAR" property="qqNumber" />
    <result column="weibo" jdbcType="VARCHAR" property="weibo" />
    <result column="signature" jdbcType="VARCHAR" property="signature" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, password, name, head_portrait, wechat, qq_number, weibo, signature
  </sql>
  <select id="selectByExample" parameterType="com.hongshen.boke.dao.object.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from boke_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from boke_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.hongshen.boke.dao.object.UserExample">
    delete from boke_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.hongshen.boke.dao.object.UserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_user (username, password, name, 
      head_portrait, wechat, qq_number, 
      weibo, signature)
    values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{headPortrait,jdbcType=VARCHAR}, #{wechat,jdbcType=VARCHAR}, #{qqNumber,jdbcType=VARCHAR}, 
      #{weibo,jdbcType=VARCHAR}, #{signature,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hongshen.boke.dao.object.UserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into boke_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="headPortrait != null">
        head_portrait,
      </if>
      <if test="wechat != null">
        wechat,
      </if>
      <if test="qqNumber != null">
        qq_number,
      </if>
      <if test="weibo != null">
        weibo,
      </if>
      <if test="signature != null">
        signature,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="headPortrait != null">
        #{headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="qqNumber != null">
        #{qqNumber,jdbcType=VARCHAR},
      </if>
      <if test="weibo != null">
        #{weibo,jdbcType=VARCHAR},
      </if>
      <if test="signature != null">
        #{signature,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.hongshen.boke.dao.object.UserExample" resultType="java.lang.Long">
    select count(*) from boke_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update boke_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.headPortrait != null">
        head_portrait = #{record.headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="record.wechat != null">
        wechat = #{record.wechat,jdbcType=VARCHAR},
      </if>
      <if test="record.qqNumber != null">
        qq_number = #{record.qqNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.weibo != null">
        weibo = #{record.weibo,jdbcType=VARCHAR},
      </if>
      <if test="record.signature != null">
        signature = #{record.signature,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update boke_user
    set id = #{record.id,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      head_portrait = #{record.headPortrait,jdbcType=VARCHAR},
      wechat = #{record.wechat,jdbcType=VARCHAR},
      qq_number = #{record.qqNumber,jdbcType=VARCHAR},
      weibo = #{record.weibo,jdbcType=VARCHAR},
      signature = #{record.signature,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.hongshen.boke.dao.object.UserDO">
    update boke_user
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="headPortrait != null">
        head_portrait = #{headPortrait,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        wechat = #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="qqNumber != null">
        qq_number = #{qqNumber,jdbcType=VARCHAR},
      </if>
      <if test="weibo != null">
        weibo = #{weibo,jdbcType=VARCHAR},
      </if>
      <if test="signature != null">
        signature = #{signature,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hongshen.boke.dao.object.UserDO">
    update boke_user
    set username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      head_portrait = #{headPortrait,jdbcType=VARCHAR},
      wechat = #{wechat,jdbcType=VARCHAR},
      qq_number = #{qqNumber,jdbcType=VARCHAR},
      weibo = #{weibo,jdbcType=VARCHAR},
      signature = #{signature,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.hongshen.boke.dao.object.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from boke_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>