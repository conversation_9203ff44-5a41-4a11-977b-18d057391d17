
@font-face {
	font-family: 'icomoon';
	src:url('/Admin/Login/fonts/icomoon.eot?rretjt');
	src:url('/Admin/Login/fonts/icomoon.eot?#iefixrretjt') format('embedded-opentype'),
		url('/Admin/Login/fonts/icomoon.woff?rretjt') format('woff'),
		url('/Admin/Login/fonts/icomoon.ttf?rretjt') format('truetype'),
		url('/Admin/Login/fonts/icomoon.svg?rretjt#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body, html {padding: 0; margin: 0;}


/* Clearfix hack by <PERSON>: http://nicolasgallagher.com/micro-clearfix-hack/ */
.clearfix:before,
.clearfix:after {
	content: " ";
	display: table;
}

.clearfix:after {
	clear: both;
}

body{
	font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans", "wenquanyi micro hei", "Hiragino Sans GB", "Hiragino Sans GB W3", "FontAwesome", sans-serif;
}
a{color: #2fa0ec;text-decoration: none;outline: none;}
a:hover,a:focus{color:#74777b;}